<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-person-gear me-2"></i>Admin Users Management
                            </h2>
                            <p class="text-light mb-0">Manage Dakoii panel administrator accounts</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/users/new') ?>" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>Add New Admin User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Admin Users List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-person-x text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Admin Users Found</h4>
                            <p class="text-muted">Start by adding your first admin user.</p>
                            <a href="<?= base_url('dakoii/users/new') ?>" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>Add Admin User
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Name</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><?= esc($user['id']) ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-person text-white"></i>
                                                    </div>
                                                    <strong><?= esc($user['username']) ?></strong>
                                                </div>
                                            </td>
                                            <td><?= esc($user['name']) ?></td>
                                            <td>
                                                <?php
                                                $roleColors = [
                                                    'super_admin' => 'danger',
                                                    'admin' => 'warning',
                                                    'moderator' => 'info'
                                                ];
                                                $color = $roleColors[$user['role']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $color ?>"><?= ucwords(str_replace('_', ' ', $user['role'])) ?></span>
                                            </td>
                                            <td>
                                                <?php if ($user['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted"><?= date('M d, Y', strtotime($user['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                 <div class="btn-group" role="group">
                                                    <a href="<?= base_url('dakoii/users/' . $user['id']) ?>" class="btn btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('dakoii/users/' . $user['id'] . '/edit') ?>" class="btn btn-outline-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <?php if ($user['id'] != session()->get('dakoii_user_id')): ?>
                                                        <form method="post" action="<?= base_url('dakoii/users/' . $user['id'] . '/delete') ?>" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this admin user?')">
                                                            <?= csrf_field() ?>
                                                            <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}
</style>
<?= $this->endSection() ?>
