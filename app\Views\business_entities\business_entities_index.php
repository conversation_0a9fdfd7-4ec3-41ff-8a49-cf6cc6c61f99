<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-building me-2"></i><?= $title ?>
            </h1>
            <p class="text-muted mb-0">Manage business entities and organizations</p>
        </div>
        <div>
            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary me-2">
                <i class="bi bi-house me-1"></i>Back to Dashboard
            </a>
            <a href="<?= base_url('admin/business-entities/new') ?>" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i>Add New Entity
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Business Entities Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="bi bi-list me-2"></i>Business Entities List
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($entities)): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" style="background-color: #f8f9fa;">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Business Name</th>
                                <th>Locations</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($entities as $index => $entity): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-info"><?= $index + 1 ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= esc($entity['business_name']) ?></strong>
                                            <?php if ($entity['remarks']): ?>
                                                <br><small class="text-muted"><?= esc($entity['remarks']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?= $entity['locations_count'] ?> locations
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = match($entity['status']) {
                                            'active' => 'success',
                                            'inactive' => 'danger',
                                            default => 'secondary'
                                        };
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>">
                                            <?= ucfirst(esc($entity['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M d, Y', strtotime($entity['created_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('admin/business-entities/' . $entity['id']) ?>" 
                                               class="btn btn-sm btn-outline-info" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="<?= base_url('admin/business-entities/' . $entity['id'] . '/edit') ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php if ($entity['locations_count'] > 0): ?>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                    disabled
                                                    title="Cannot delete - Entity has <?= $entity['locations_count'] ?> location(s)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php else: ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="confirmDelete(<?= $entity['id'] ?>, '<?= esc($entity['business_name']) ?>')"
                                                    title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h6 class="text-muted">No Business Entities Found</h6>
                    <p class="text-muted small">
                        Start by creating your first business entity to organize your locations.
                    </p>
                    <a href="<?= base_url('admin/business-entities/new') ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Create First Entity
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the business entity "<span id="entityName"></span>"?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, name) {
    document.getElementById('entityName').textContent = name;
    document.getElementById('deleteForm').action = '<?= base_url('admin/business-entities') ?>/' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>
