<?php

namespace App\Models;

use CodeIgniter\Model;

class GoodsGroupModel extends Model
{
    protected $table = 'goods_groups';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'group_name',
        'description',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'group_name' => 'required|max_length[100]|is_unique[goods_groups.group_name,id,{id}]',
        'description' => 'permit_empty|max_length[65535]',
        'status' => 'required|in_list[active,inactive]',
        'status_by' => 'permit_empty|integer',
        'status_remarks' => 'permit_empty|max_length[65535]',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'group_name' => [
            'required' => 'Group name is required',
            'max_length' => 'Group name cannot exceed 100 characters',
            'is_unique' => 'Group name already exists'
        ],
        'description' => [
            'max_length' => 'Description is too long'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be either active or inactive'
        ],
        'status_by' => [
            'integer' => 'Invalid user ID for status_by'
        ],
        'created_by' => [
            'integer' => 'Invalid user ID for created_by'
        ],
        'updated_by' => [
            'integer' => 'Invalid user ID for updated_by'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Get all active goods groups for dropdown
     */
    public function getActiveGroupsForDropdown()
    {
        $groups = $this->where('status', 'active')
                      ->where('is_deleted', false)
                      ->orderBy('group_name', 'ASC')
                      ->findAll();
        
        $dropdown = [];
        foreach ($groups as $group) {
            $dropdown[$group['id']] = $group['group_name'];
        }
        
        return $dropdown;
    }
    
    /**
     * Get goods groups with brand count using simple operations
     */
    public function getGroupsWithBrandCount()
    {
        $groups = $this->where('is_deleted', false)
                      ->orderBy('group_name', 'ASC')
                      ->findAll();

        // Add brand count to each group using simple operations
        $goodsBrandModel = new \App\Models\GoodsBrandModel();
        foreach ($groups as &$group) {
            $group['brand_count'] = $goodsBrandModel->where('goods_group_id', $group['id'])
                                                   ->where('is_deleted', false)
                                                   ->countAllResults();
        }

        return $groups;
    }
    
    /**
     * Get active goods groups
     */
    public function getActiveGroups()
    {
        return $this->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('group_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Search goods groups
     */
    public function searchGroups(string $keyword)
    {
        return $this->groupStart()
                   ->like('group_name', $keyword)
                   ->orLike('description', $keyword)
                   ->groupEnd()
                   ->where('is_deleted', false)
                   ->orderBy('group_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get group statistics
     */
    public function getGroupStats()
    {
        $stats = [];
        
        // Total groups
        $stats['total'] = $this->where('is_deleted', false)->countAllResults();
        
        // Active groups
        $stats['active'] = $this->where('status', 'active')
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        // Inactive groups
        $stats['inactive'] = $this->where('status', 'inactive')
                                 ->where('is_deleted', false)
                                 ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Update group status
     */
    public function updateStatus(int $groupId, string $status, int $userId, string $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => $userId
        ];
        
        if ($remarks) {
            $data['status_remarks'] = $remarks;
        }
        
        return $this->update($groupId, $data);
    }
    
    /**
     * Check if group has brands
     */
    public function hasBrands(int $groupId): bool
    {
        $brandModel = new \App\Models\GoodsBrandModel();
        return $brandModel->where('goods_group_id', $groupId)
                         ->where('is_deleted', false)
                         ->countAllResults() > 0;
    }
    
    /**
     * Check if group has items
     */
    public function hasItems(int $groupId): bool
    {
        $itemModel = new \App\Models\GoodsItemModel();
        return $itemModel->where('goods_group_id', $groupId)
                        ->where('is_deleted', false)
                        ->countAllResults() > 0;
    }
}
