# Price Data Collection System - Features List

## Dakoii Portal Features (Super Administrator)

### Organizational Management
- **Multi-Organization Setup**: Create and configure multiple organizations (government departments, NGOs, commercial entities) within a single system instance
- **Organization Hierarchy Management**: Define organizational structures and relationships between different surveillance entities
- **Organization-Specific Configuration**: Set operational parameters, data access boundaries, and system permissions for each organization
- **Cross-Organization Coordination**: Enable authorized inter-organizational data sharing and collaborative surveillance programs

### Geographic Administration
- **Country Management**: Create and maintain country-level geographic entities for international operations
- **Province Administration**: Configure provincial boundaries and administrative divisions across Papua New Guinea
- **District Management**: Set up district-level geographic divisions for granular operational assignment and reporting
- **Geographic Hierarchy Validation**: Ensure proper relationships between countries, provinces, and districts with referential integrity
- **Geographic Data Import/Export**: Support bulk geographic data updates from government sources and mapping systems

### System-Wide User Administration
- **Organization Administrator Creation**: Create and assign organization-level administrators with appropriate scope and permissions
- **Super Admin Management**: Manage super administrator accounts with system-wide access and configuration capabilities
- **Role Template Management**: Define and maintain role templates that organizations can customize for their specific needs
- **System Access Control**: Configure system-wide security policies and access restrictions
- **User Activity Monitoring**: Track user activities across all organizations for security and audit purposes

### System Configuration
- **Global System Settings**: Configure system-wide parameters including security policies, data retention rules, and operational standards
- **Integration Management**: Set up and manage integrations with external systems including government databases and research institutions
- **System Backup Configuration**: Configure automated backup procedures and disaster recovery protocols
- **Performance Monitoring Setup**: Configure system monitoring, alerting thresholds, and performance tracking parameters
- **Audit Trail Configuration**: Set up comprehensive audit logging for all system activities and data modifications

## Admin Portal Features (Organization Management)

### Business Entity Management
- **Entity Registration**: Register commercial establishments including retail chains, wholesalers, markets, and specialized outlets
- **Multi-Location Support**: Configure multiple physical locations for businesses operating across different geographic areas
- **Entity Classification**: Categorize businesses by type, size, market focus, and operational characteristics
- **Entity Profile Management**: Maintain detailed business profiles including contact information, operational hours, and management details
- **Entity Relationship Mapping**: Define relationships between business entities such as parent-subsidiary structures or franchise relationships

### Goods Classification System
- **Goods Group Management**: Create and maintain broad product categories such as "Rice," "Cooking Oil," or "Fresh Produce"
- **Goods Brand Administration**: Manage specific product brands with primary/substitute classifications for market analysis
- **Goods Item Specification**: Define granular product specifications including package sizes, units of measure, and product variants
- **Hierarchical Product Structure**: Maintain three-tier classification system (Groups → Brands → Items) with full referential integrity
- **Product Import/Export**: Support bulk product data management and integration with external product databases

### Workflow Management
- **Exercise Planning**: Create comprehensive surveillance campaigns with defined objectives, scope, and timelines
- **Workplan Development**: Break down exercises into manageable workplans with specific geographic and operational focus
- **Activity Configuration**: Define specific activities within workplans including price collection, market surveys, and analytical studies
- **Task Creation and Assignment**: Generate specific task assignments for field officers with detailed instructions and requirements
- **Workflow Templates**: Create reusable workflow templates for common surveillance operations

### Task Assignment and Coordination
- **Officer Assignment Management**: Assign tasks to field officers based on geographic coverage, expertise, and workload capacity
- **Geographic Assignment Optimization**: Optimize task assignments based on travel efficiency and coverage requirements
- **Deadline Management**: Set and track task deadlines with automated reminders and escalation procedures
- **Priority Management**: Assign task priorities and manage urgent surveillance requirements
- **Workload Balancing**: Monitor and balance workloads across field officers to ensure equitable assignment distribution

### Approval and Quality Assurance
- **Multi-Level Approval Workflows**: Configure approval processes that ensure data quality through supervisor review
- **Data Validation Rules**: Set up automated validation checks for price data completeness, accuracy, and consistency
- **Exception Handling**: Manage data anomalies and exceptions through structured review and correction processes
- **Quality Metrics Tracking**: Monitor data quality indicators and officer performance metrics
- **Approval Analytics**: Track approval rates, rejection patterns, and quality improvement trends

### Reporting and Analytics
- **Market Analysis Reports**: Generate comprehensive reports on price trends, geographic variations, and market dynamics
- **Comparative Analysis**: Compare prices across different business entities, geographic regions, and time periods
- **Operational Performance Reports**: Track data collection efficiency, officer productivity, and workflow performance
- **Custom Report Builder**: Create custom reports with flexible filtering, grouping, and visualization options
- **Dashboard Configuration**: Set up operational dashboards with key performance indicators and real-time status updates

### User Management
- **Admin User Creation**: Create and manage administrator accounts within organizational scope
- **Supervisor Management**: Set up supervisor accounts with appropriate approval and oversight permissions
- **Field Officer Management**: Manage field officer accounts with mobile access and task assignment capabilities
- **Guest User Access**: Provide read-only access to stakeholders requiring surveillance data visibility
- **Role-Based Permissions**: Configure detailed permission sets based on user roles and operational requirements

## Field Portal Features (Data Collection Interface)

### Mobile-Optimized Interface
- **Responsive Design**: Adaptive interface that functions effectively across smartphones, tablets, and desktop devices
- **Touch-Friendly Navigation**: Optimized touch interfaces for efficient data entry using mobile devices
- **Minimal Data Usage**: Efficient interface design that reduces bandwidth requirements for low-connectivity environments
- **Fast Loading**: Streamlined interface components that load quickly even with limited internet connectivity
- **Offline-First Design**: Interface design that prioritizes offline functionality with sync capabilities

### Task Management
- **Task Assignment Display**: Clear presentation of assigned tasks with priorities, deadlines, and detailed instructions
- **Task Progress Tracking**: Visual progress indicators showing completion status and remaining requirements
- **Task History**: Access to previously completed tasks for reference and pattern analysis
- **Task Filtering and Sorting**: Organize tasks by priority, deadline, location, or completion status
- **Task Comments and Notes**: Add contextual notes and observations during task execution

### Data Collection Forms
- **Guided Data Entry**: Step-by-step data collection forms that ensure completeness and accuracy
- **Dynamic Form Validation**: Real-time validation that prevents common data entry errors
- **Barcode Scanning Integration**: Support for barcode scanning to improve product identification accuracy
- **Photo Documentation**: Capture photos of products, price tags, and store conditions for verification
- **Voice Notes**: Record voice observations and notes for complex market conditions or unusual circumstances

### Offline Capabilities
- **Offline Task Download**: Download task assignments and data entry forms while connected for offline completion
- **Offline Data Storage**: Secure local storage of collected data during periods without connectivity
- **Automatic Synchronization**: Automatic data sync when connectivity becomes available
- **Conflict Resolution**: Handle data conflicts that may arise during synchronization processes
- **Offline Map Access**: Access to location maps and navigation without requiring continuous internet connection

### Location and Navigation
- **GPS Integration**: Automatic location capture for verification of data collection locations
- **Map Integration**: Visual maps showing task locations and optimal routes for field collection
- **Location Verification**: Confirm that data collection occurs at assigned business locations
- **Distance Tracking**: Track travel distances and time spent at each location for operational analytics
- **Location History**: Maintain history of visited locations for route optimization and coverage analysis

### Data Submission and Validation
- **Batch Data Submission**: Submit multiple price collections efficiently when connectivity permits
- **Submission Validation**: Client-side validation that ensures data completeness before submission
- **Submission Status Tracking**: Track submission status and receive confirmation of successful data transfer
- **Error Handling**: Clear error messages and correction guidance for failed submissions
- **Resubmission Capabilities**: Easy resubmission of failed data transfers without data loss

## Cross-Platform System Features

### Security and Authentication
- **Multi-Factor Authentication**: Enhanced security through SMS, email, or authenticator app verification
- **JWT Token Management**: Secure, stateless authentication suitable for mobile and web interfaces
- **Session Management**: Flexible session handling that accommodates field operation requirements
- **Password Policy Enforcement**: Configurable password requirements and expiration policies
- **Account Lockout Protection**: Automatic account protection against brute force attacks

### Data Integration and Export
- **RESTful API Access**: Comprehensive API for external system integration and data access
- **Multiple Export Formats**: Support for CSV, JSON, XML, and PDF export formats
- **Scheduled Data Exports**: Automated data exports to authorized external systems
- **Data Import Capabilities**: Import geographic data, business information, and product catalogs from external sources
- **Webhook Notifications**: Real-time notifications to external systems for critical events

### Performance and Scalability
- **Intelligent Caching**: Multi-level caching that improves performance while ensuring data consistency
- **Load Balancing**: Horizontal scaling capabilities that support increasing user loads
- **Database Optimization**: Query optimization and indexing strategies for efficient data retrieval
- **CDN Integration**: Content delivery network support for improved global performance
- **Performance Monitoring**: Comprehensive monitoring of system performance and user experience metrics

### Audit and Compliance
- **Comprehensive Audit Trails**: Complete logging of all system activities, data modifications, and user actions
- **Data Retention Management**: Configurable data retention policies that comply with regulatory requirements
- **Compliance Reporting**: Generate reports that demonstrate compliance with data protection and privacy regulations
- **Data Anonymization**: Tools for anonymizing sensitive data for research and analysis purposes
- **Backup and Recovery**: Automated backup procedures with tested disaster recovery capabilities

### Analytics and Business Intelligence
- **Real-Time Dashboards**: Live dashboards showing current market conditions and operational status
- **Trend Analysis**: Identify and analyze price trends across different time periods and market segments
- **Geographic Analysis**: Map-based visualization of price variations across different regions
- **Comparative Analysis**: Compare price patterns across different business types, brands, and locations
- **Statistical Analysis**: Advanced statistical tools for market research and economic analysis

### Communication and Collaboration
- **In-System Messaging**: Communication tools for coordination between supervisors and field officers
- **Notification System**: Automated notifications for task assignments, approvals, and system updates
- **Announcement Management**: System-wide and organization-specific announcement capabilities
- **Document Management**: Shared document storage for operational procedures, guidelines, and reference materials
- **Training Resources**: Integrated access to training materials and system documentation

### Customization and Configuration
- **Configurable Workflows**: Customize approval processes and operational workflows based on organizational needs
- **Custom Field Support**: Add custom data fields for organization-specific requirements
- **Brand Customization**: Customize interface appearance with organizational branding and themes
- **Report Customization**: Create custom report layouts and formats for different stakeholder needs
- **Dashboard Personalization**: Personalized dashboard configurations for different user roles and preferences

This comprehensive feature set ensures that the Price Data Collection System supports sophisticated market surveillance operations while remaining accessible and efficient for users across all levels of technical expertise and operational requirements.


Detailed Features List for Price Data Collection System
This document outlines the comprehensive features of the Price Data Collection System, built using CodeIgniter 4 and MySQL. The system is designed to manage price surveillance data for goods across various business entities through three portals: Dakoii Portal, Admin Portal, and Field Portal. The features are categorized by portal and functionality, ensuring alignment with the system’s requirements for scalability, usability, and efficiency in low-bandwidth environments.

1. Dakoii Portal (Super Admin)
The Dakoii Portal is designed for super admins to manage organizations, geographical locations, and user roles at the highest level.
1.1. Organization Management

Create Organization: Add new organizations with details such as name and description.
Edit Organization: Update organization details, including name and status (active/inactive).
Delete Organization: Remove organizations (with confirmation to prevent accidental deletion).
List Organizations: View a paginated, searchable table of all organizations with details like creation date and status.

1.2. Geographical Location Management

Create Location: Define geographical locations (country, province, district) for price collection activities.
Edit Location: Modify location details, ensuring relational integrity with business locations.
Delete Location: Remove unused locations (with checks to prevent deletion if linked to entities).
List Locations: Display a hierarchical view of locations (e.g., country → province → district) with search and filter options.

1.3. User Management

Create Admin Users: Assign admin roles to users within an organization, specifying username, password, and contact details.
Role-Based Access Control (RBAC): Assign super admin or admin roles with specific permissions.
Edit User: Update user details, including role and organization assignment.
Deactivate User: Temporarily disable user accounts without deleting them.
List Users: View a table of users with filters for organization and role.

1.4. Audit and Logging

Activity Log: Track all super admin actions (e.g., organization creation, user assignment) with timestamps and details.
Export Logs: Download activity logs in CSV format for auditing purposes.


2. Admin Portal (Admin and Supervisor)
The Admin Portal is used by admins and supervisors to manage goods, business entities, workplans, tasks, approvals, and reports.
2.1. Goods Management

Goods Group Management:
Create goods groups (e.g., Rice, Sugar, Flour).
Edit or delete existing groups.
List groups with details like creation date and number of associated brands.


Goods Brand Management:
Add brands under a goods group (e.g., Roots Rice, Trukai Rice) with type (primary or substitute).
Edit or delete brands, ensuring no linked items exist before deletion.
List brands with filters for group and type.


Goods Item Management:
Create items under a brand (e.g., Roots Rice 10kg, 5kg) with unit specifications.
Edit or delete items, with validation to prevent deletion if linked to price data.
List items with details like brand, unit, and associated tasks.



2.2. Business Entity Management

Entity Registration:
Register business houses (e.g., Papindo, Andersons Foodland).
Specify entity details like name and contact information.


Business Location Management:
Add multiple business locations per entity (e.g., Papindo Wewak Town, Papindo 8/6).
Link locations to geographical data (province, district).
Edit or delete locations, with checks for linked tasks or price data.


List Entities and Locations:
Display entities and their locations in a searchable, paginated table.
Filter by geographical location or entity name.



2.3. Workplan and Task Management

Workplan Creation:
Create workplans with names and descriptions, assigned to supervisors.
Set start and end dates for workplans.


Activity Creation:
Add activities under a workplan (type: price collection).
Specify activity details like target goods and locations.


Task Assignment:
Create tasks under activities, specifying goods items, business locations, and assigned field officers.
Set task deadlines and priorities.


Task Status Tracking:
View tasks with statuses (pending, submitted, approved, rejected).
Filter tasks by field officer, status, or workplan.


Task Approval (Supervisor Only):
Review submitted price data for accuracy.
Approve or reject tasks with comments for field officers.
Bulk approval/rejection for efficiency.



2.4. Price Data Management

View Price Data:
Display collected price data in tabular format, with filters for goods, entities, locations, and quarters.
Support for sorting by price, date, or quarter.


Edit Price Data:
Admins can edit submitted price data (with audit logging).
Validation to ensure prices are numeric and within reasonable ranges.


Delete Price Data:
Remove incorrect price entries (with confirmation and logging).



2.5. Reporting and Analytics

Price Reports:
Generate reports comparing prices across quarters, goods, or business locations.
Support for retail and wholesale price reports (primary and substitute goods).
Example: Average price of Roots Rice 10kg across all shops in Q2 2025.


Export Reports:
Export reports in CSV or Excel format for external analysis.


Visualizations:
Display price trends using charts (e.g., line charts for price changes over quarters).
Use DataTables for interactive tables with search and filter capabilities.


Summary Dashboards:
Provide admins with a dashboard showing key metrics (e.g., number of tasks completed, average prices, compliance rates).



2.6. User Management (Admin Only)

Create Users: Add supervisors, field officers, or guest users within the organization.
Edit/Delete Users: Modify user details or deactivate accounts.
Assign Roles: Define permissions for supervisors (task creation, approval) and field officers (data submission).
List Users: View all users in the organization with role-based filters.


3. Field Portal (Field Officers)
The Field Portal is a lightweight interface optimized for field officers to complete price collection tasks in low-bandwidth environments.
3.1. Task Management

View Assigned Tasks:
Display a list of tasks assigned to the logged-in field officer.
Show task details like goods item, business location, deadline, and status.
Filter tasks by status (pending, submitted) or due date.


Task Details:
Provide detailed instructions for each task (e.g., collect price for Roots Rice 10kg at Papindo Wewak Town).



3.2. Price Data Submission

Submit Price Data:
Simple form to enter price data for a specific goods item and location.
Validation for numeric input and mandatory fields (e.g., price, collection date).
Support for selecting quarter (1st, 2nd, 3rd, 4th).


Offline Data Entry:
Cache price data locally using HTML5 Web Storage when offline.
Sync data with the server when connectivity is restored.


Task Status Update:
Mark tasks as submitted after price data entry.
View feedback from supervisors (e.g., rejection comments).



3.3. Minimalist Design

Lightweight UI:
Use minimal CSS and JavaScript to ensure fast loading in low-bandwidth areas.
Responsive design optimized for mobile devices.


Simplified Navigation:
Single-page application (SPA) approach with AJAX for task retrieval and submission.
Minimal menu with only essential options (e.g., view tasks, submit data).




4. Common Features Across Portals
These features are available across all portals to ensure consistency and usability.
4.1. Authentication and Security

User Login: Secure login with username and password, using CodeIgniter’s session management.
Role-Based Access Control (RBAC):
Super Admin: Full access to Dakoii Portal.
Admin: Full access to Admin Portal, including user management.
Supervisor: Access to task creation, approval, and reporting in Admin Portal.
Field Officer: Access to Field Portal for task viewing and data submission.
Guest: Read-only access to reports (if permitted).


Password Management:
Password reset via email (with secure token generation).
Enforce strong password policies.


Security Measures:
HTTPS for secure data transmission.
Input sanitization to prevent SQL injection and XSS attacks.
CSRF protection using CodeIgniter’s built-in security features.



4.2. Data Export and Import

Export Data:
Export goods, entities, tasks, or price data in CSV/Excel format.
Support for filtering exported data (e.g., by quarter or goods group).


Import Data:
Import goods or entity data from CSV/Excel files using PhpSpreadsheet.
Validate imported data to ensure consistency with database schema.



4.3. Notifications

Task Notifications:
Notify field officers of new task assignments via in-app notifications.
Notify supervisors of pending task approvals.


System Alerts:
Alert admins of critical issues (e.g., data inconsistencies, overdue tasks).



4.4. Audit and Logging

Activity Logging:
Log all user actions (e.g., task creation, price submission, approvals) with timestamps and user IDs.


Audit Trail:
Provide admins with access to detailed audit logs for troubleshooting and compliance.



4.5. Responsive Design

Mobile-Friendly UI:
Ensure all portals are fully responsive for desktops, tablets, and smartphones.


Cross-Browser Compatibility:
Support for modern browsers (Chrome, Firefox, Safari, Edge).




5. Reporting and Analytics Features
These features focus on generating actionable insights from collected price data.
5.1. Price Surveillance Reports

Retail Price Reports:
Aggregate retail prices for primary and substitute goods by quarter, shop, or goods item.
Example: Average price of Trukai Rice 10kg across all shops in Q2 2025.


Wholesale Price Reports:
Aggregate wholesale prices for primary and substitute goods by bale/bag.
Example: Wholesale price trends for Ramu Sugar 10x1kg across wholesalers.


Comparison Reports:
Compare prices across quarters, shops, or regions (e.g., Momase).
Highlight price changes (increases/decreases) with visual indicators.



5.2. Basic Goods Reports

Essential Items Reports:
Generate reports for basic goods (e.g., tinned fish, cooking oils, biscuits) as per the provided survey sheets.
Example: Price of Besta Oil 425g across retailers in Q1 2025.


Compliance Reports:
Track compliance with price collection tasks (e.g., percentage of tasks completed on time).



5.3. Data Visualization

Interactive Tables:
Use DataTables for sortable, searchable tables of price data.


Charts:
Line charts for price trends over time.
Bar charts for comparing prices across shops or goods.


Export Visualizations:
Export charts as PNG or PDF for inclusion in reports.




6. Performance and Scalability Features

Optimized Queries:
Use MySQL indexes on frequently queried fields (e.g., goods_item_id, collection_date).
Implement query caching in CodeIgniter for repetitive queries.


Low-Bandwidth Optimization:
Compress API responses (e.g., Gzip compression).
Minimize asset sizes (CSS, JavaScript) for Field Portal.


Scalability:
Support for database partitioning on price_data table by quarter or date.
Horizontal scaling with read replicas for MySQL in high-traffic scenarios.




7. Future-Ready Features

Offline Enhancements:
Implement Progressive Web App (PWA) capabilities for Field Portal to improve offline functionality.


Mobile App Integration:
Support for a native mobile app for Field Portal using Flutter or React Native.


API Integration:
Expose RESTful APIs for third-party integrations (e.g., via xAI API at https://x.ai/api).


Advanced Analytics:
Integrate machine learning models for price trend prediction (future integration with xAI services).




This features list ensures the Price Data Collection System meets the requirements outlined in the provided documents, delivering a robust, user-friendly, and scalable solution for price surveillance across multiple portals.