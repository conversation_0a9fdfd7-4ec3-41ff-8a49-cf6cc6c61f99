<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit Admin User
                            </h2>
                            <p class="text-light mb-0">Update administrator account: <strong><?= esc($user['username']) ?></strong></p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-person-gear me-2"></i>Admin User Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('dakoii/users/' . $user['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label text-light">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= old('username', $user['username']) ?>" required>
                                <div class="form-text text-muted">Must be unique and at least 3 characters</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label text-light">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name', $user['name']) ?>" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label text-light">New Password</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text text-muted">Leave blank to keep current password. Minimum 6 characters if changing.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label text-light">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label text-light">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="super_admin" <?= old('role', $user['role']) == 'super_admin' ? 'selected' : '' ?>>Super Admin</option>
                                    <option value="admin" <?= old('role', $user['role']) == 'admin' ? 'selected' : '' ?>>Admin</option>
                                    <option value="moderator" <?= old('role', $user['role']) == 'moderator' ? 'selected' : '' ?>>Moderator</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="is_active" class="form-label text-light">Status</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1" <?= old('is_active', $user['is_active']) == '1' ? 'selected' : '' ?>>Active</option>
                                    <option value="0" <?= old('is_active', $user['is_active']) == '0' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                        </div>



                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary">
                                    <div class="card-body">
                                        <h6 class="text-light">Account Information</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted">Created: <?= date('M d, Y H:i', strtotime($user['created_at'])) ?></small>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">Last Updated: <?= $user['updated_at'] ? date('M d, Y H:i', strtotime($user['updated_at'])) : 'Never' ?></small>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>Update Admin User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
