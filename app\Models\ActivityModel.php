<?php

namespace App\Models;

use CodeIgniter\Model;

class ActivityModel extends Model
{
    protected $table = 'activities';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'org_id',
        'workplan_id',
        'activity_type',
        'activity_name',
        'date_from',
        'date_to',
        'remarks',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'org_id' => 'required|integer',
        'workplan_id' => 'required|integer',
        'activity_type' => 'required|max_length[50]',
        'activity_name' => 'required|max_length[200]',
        'date_from' => 'required|valid_date',
        'date_to' => 'required|valid_date',
        'remarks' => 'permit_empty|max_length[65535]',
        'status' => 'required|in_list[active,submitted,approved,redo,cancelled]',
        'status_by' => 'permit_empty|integer',
        'status_remarks' => 'permit_empty|max_length[65535]',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization is required',
            'integer' => 'Invalid organization selection'
        ],
        'workplan_id' => [
            'required' => 'Workplan is required',
            'integer' => 'Invalid workplan selection'
        ],
        'activity_type' => [
            'required' => 'Activity type is required',
            'max_length' => 'Activity type cannot exceed 50 characters'
        ],
        'activity_name' => [
            'required' => 'Activity name is required',
            'max_length' => 'Activity name cannot exceed 200 characters'
        ],
        'date_from' => [
            'required' => 'Start date is required',
            'valid_date' => 'Invalid start date format'
        ],
        'date_to' => [
            'required' => 'End date is required',
            'valid_date' => 'Invalid end date format'
        ],
        'remarks' => [
            'max_length' => 'Remarks is too long'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, submitted, approved, redo, or cancelled'
        ],
        'status_by' => [
            'integer' => 'Invalid user ID for status_by'
        ],
        'created_by' => [
            'integer' => 'Invalid user ID for created_by'
        ],
        'updated_by' => [
            'integer' => 'Invalid user ID for updated_by'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Custom validation to check date range
     */
    protected $beforeInsert = ['validateDateRange'];
    protected $beforeUpdate = ['validateDateRange'];
    
    protected function validateDateRange(array $data)
    {
        if (isset($data['data']['date_from']) && isset($data['data']['date_to'])) {
            $dateFrom = strtotime($data['data']['date_from']);
            $dateTo = strtotime($data['data']['date_to']);
            
            if ($dateFrom >= $dateTo) {
                throw new \CodeIgniter\Database\Exceptions\DatabaseException('End date must be after start date');
            }
        }
        
        return $data;
    }
    
    /**
     * Get activities by workplan
     */
    public function getActivitiesByWorkplan(int $workplanId)
    {
        return $this->where('workplan_id', $workplanId)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get activities by organization
     */
    public function getActivitiesByOrg(int $orgId)
    {
        return $this->where('org_id', $orgId)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get activities by type
     */
    public function getActivitiesByType(int $orgId, string $activityType)
    {
        return $this->where('org_id', $orgId)
                   ->where('activity_type', $activityType)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get activities by status
     */
    public function getActivitiesByStatus(int $orgId, string $status)
    {
        return $this->where('org_id', $orgId)
                   ->where('status', $status)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get activities within date range
     */
    public function getActivitiesInDateRange(int $orgId, string $dateFrom, string $dateTo)
    {
        return $this->where('org_id', $orgId)
                   ->where('date_from >=', $dateFrom)
                   ->where('date_to <=', $dateTo)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'ASC')
                   ->findAll();
    }
    
    /**
     * Update activity status
     */
    public function updateStatus(int $activityId, string $status, int $userId, string $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => $userId
        ];
        
        if ($remarks) {
            $data['status_remarks'] = $remarks;
        }
        
        return $this->update($activityId, $data);
    }
    
    /**
     * Get activity statistics by organization
     */
    public function getActivityStats(int $orgId)
    {
        $stats = [];
        
        // Total activities
        $stats['total'] = $this->where('org_id', $orgId)
                              ->where('is_deleted', false)
                              ->countAllResults();
        
        // Active activities
        $stats['active'] = $this->where('org_id', $orgId)
                               ->where('status', 'active')
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        // Submitted activities
        $stats['submitted'] = $this->where('org_id', $orgId)
                                  ->where('status', 'submitted')
                                  ->where('is_deleted', false)
                                  ->countAllResults();
        
        // Approved activities
        $stats['approved'] = $this->where('org_id', $orgId)
                                 ->where('status', 'approved')
                                 ->where('is_deleted', false)
                                 ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Search activities
     */
    public function searchActivities(int $orgId, string $keyword)
    {
        return $this->where('org_id', $orgId)
                   ->groupStart()
                       ->like('activity_name', $keyword)
                       ->orLike('activity_type', $keyword)
                       ->orLike('remarks', $keyword)
                   ->groupEnd()
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get activity with workplan details
     */
    public function getActivityWithWorkplan(int $activityId)
    {
        return $this->select('activities.*, workplans.title as workplan_title')
                   ->join('workplans', 'workplans.id = activities.workplan_id', 'left')
                   ->where('activities.id', $activityId)
                   ->where('activities.is_deleted', false)
                   ->first();
    }
    
    /**
     * Get active activities for dropdown by workplan
     */
    public function getActiveActivitiesForDropdown(int $workplanId)
    {
        $activities = $this->where('workplan_id', $workplanId)
                          ->where('status', 'active')
                          ->where('is_deleted', false)
                          ->orderBy('activity_name', 'ASC')
                          ->findAll();
        
        $dropdown = [];
        foreach ($activities as $activity) {
            $dropdown[$activity['id']] = $activity['activity_name'];
        }
        
        return $dropdown;
    }
}
