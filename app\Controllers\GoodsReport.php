<?php

namespace App\Controllers;

use App\Models\GoodsGroupModel;
use App\Models\GoodsBrandModel;
use App\Models\GoodsItemModel;

class GoodsReport extends BaseController
{
    protected $goodsGroupModel;
    protected $goodsBrandModel;
    protected $goodsItemModel;

    public function __construct()
    {
        $this->goodsGroupModel = new GoodsGroupModel();
        $this->goodsBrandModel = new GoodsBrandModel();
        $this->goodsItemModel = new GoodsItemModel();
    }

    /**
     * Display goods report
     */
    public function index()
    {
        // Check if admin is logged in
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the admin portal.');
        }

        // Get all items with their group and brand information
        $items = $this->goodsItemModel->where('is_deleted', false)
                                     ->orderBy('id', 'ASC')
                                     ->findAll();

        // Add group and brand information to each item
        foreach ($items as &$item) {
            // Get group information
            $group = $this->goodsGroupModel->find($item['goods_group_id']);
            $item['group_name'] = $group ? $group['group_name'] : 'Unknown';
            $item['group_description'] = $group ? $group['description'] : '';

            // Get brand information
            $brand = $this->goodsBrandModel->find($item['goods_brand_id']);
            $item['brand_name'] = $brand ? $brand['brand_name'] : 'Unknown';
            $item['brand_type'] = $brand ? $brand['type'] : 'unknown';
        }

        $data = [
            'title' => 'Goods Report',
            'items' => $items
        ];

        return view('goods_report/goods_report_index', $data);
    }
}
