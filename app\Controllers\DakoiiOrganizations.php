<?php

namespace App\Controllers;

use App\Models\DakoiiOrgModel;
use App\Models\GeoCountryModel;
use App\Models\GeoProvinceModel;
use CodeIgniter\Controller;

class DakoiiOrganizations extends Controller
{
    protected $dakoiiOrgModel;
    protected $geoCountryModel;
    protected $geoProvinceModel;
    
    public function __construct()
    {
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        $this->geoCountryModel = new GeoCountryModel();
        $this->geoProvinceModel = new GeoProvinceModel();
        helper('dakoii');
    }
    
    /**
     * Check if user is logged in
     */
    private function checkAuth()
    {
        if (!session()->get('dakoii_logged_in')) {
            return redirect()->to('dakoii')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of organizations
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $organizations = $this->dakoiiOrgModel->getOrgsWithLocation();
        
        $data = [
            'title' => 'Organizations Management',
            'organizations' => $organizations
        ];
        
        return view('dakoii_organizations/dakoii_organizations_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $countries = $this->geoCountryModel->getCountriesForDropdown();
        
        $data = [
            'title' => 'Add New Organization',
            'organization' => [],
            'countries' => $countries,
            'provinces' => []
        ];
        
        return view('dakoii_organizations/dakoii_organizations_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'org_code' => 'required|max_length[100]|is_unique[dakoii_org.org_code]',
            'org_name' => 'required|max_length[255]',
            'description' => 'permit_empty',
            'province_id' => 'permit_empty|integer',
            'country_id' => 'permit_empty|integer',
            'postal_address' => 'permit_empty',
            'phone_numbers' => 'permit_empty|max_length[200]',
            'email_addresses' => 'permit_empty|valid_email|max_length[500]',
            'is_locationlocked' => 'in_list[0,1]',
            'is_active' => 'in_list[0,1]',
            'license_status' => 'permit_empty|max_length[50]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'org_code' => $this->request->getPost('org_code'),
            'org_name' => $this->request->getPost('org_name'),
            'description' => $this->request->getPost('description'),
            'province_id' => $this->request->getPost('province_id') ?: null,
            'country_id' => $this->request->getPost('country_id') ?: null,
            'postal_address' => $this->request->getPost('postal_address'),
            'phone_numbers' => $this->request->getPost('phone_numbers'),
            'email_addresses' => $this->request->getPost('email_addresses'),
            'is_locationlocked' => $this->request->getPost('is_locationlocked') ?? 0,
            'is_active' => $this->request->getPost('is_active') ?? 1,
            'license_status' => $this->request->getPost('license_status'),
            'created_by' => session()->get('dakoii_user_id')
        ];
        
        if ($this->dakoiiOrgModel->save($data)) {
            return redirect()->to('dakoii/organizations')->with('success', 'Organization created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create organization.');
        }
    }
    
    /**
     * Show single organization
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $organization = $this->dakoiiOrgModel->select('dakoii_org.*, geo_countries.name as country_name, geo_provinces.name as province_name')
                                           ->join('geo_countries', 'geo_countries.id = dakoii_org.country_id', 'left')
                                           ->join('geo_provinces', 'geo_provinces.id = dakoii_org.province_id', 'left')
                                           ->find($id);
        
        if (!$organization) {
            return redirect()->to('dakoii/organizations')->with('error', 'Organization not found.');
        }
        
        $data = [
            'title' => 'View Organization',
            'organization' => $organization
        ];
        
        return view('dakoii_organizations/dakoii_organizations_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $organization = $this->dakoiiOrgModel->find($id);
        
        if (!$organization) {
            return redirect()->to('dakoii/organizations')->with('error', 'Organization not found.');
        }
        
        $countries = $this->geoCountryModel->getCountriesForDropdown();
        $provinces = [];
        
        if ($organization['country_id']) {
            $provinces = $this->geoProvinceModel->getProvincesForDropdown($organization['country_id']);
        }
        
        $data = [
            'title' => 'Edit Organization',
            'organization' => $organization,
            'countries' => $countries,
            'provinces' => $provinces
        ];
        
        return view('dakoii_organizations/dakoii_organizations_edit', $data);
    }
    
    /**
     * Handle edit form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $organization = $this->dakoiiOrgModel->find($id);
        
        if (!$organization) {
            return redirect()->to('dakoii/organizations')->with('error', 'Organization not found.');
        }
        
        $rules = [
            'org_code' => "required|max_length[100]|is_unique[dakoii_org.org_code,id,{$id}]",
            'org_name' => 'required|max_length[255]',
            'description' => 'permit_empty',
            'province_id' => 'permit_empty|integer',
            'country_id' => 'permit_empty|integer',
            'postal_address' => 'permit_empty',
            'phone_numbers' => 'permit_empty|max_length[200]',
            'email_addresses' => 'permit_empty|valid_email|max_length[500]',
            'is_locationlocked' => 'in_list[0,1]',
            'is_active' => 'in_list[0,1]',
            'license_status' => 'permit_empty|max_length[50]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'org_code' => $this->request->getPost('org_code'),
            'org_name' => $this->request->getPost('org_name'),
            'description' => $this->request->getPost('description'),
            'province_id' => $this->request->getPost('province_id') ?: null,
            'country_id' => $this->request->getPost('country_id') ?: null,
            'postal_address' => $this->request->getPost('postal_address'),
            'phone_numbers' => $this->request->getPost('phone_numbers'),
            'email_addresses' => $this->request->getPost('email_addresses'),
            'is_locationlocked' => $this->request->getPost('is_locationlocked') ?? 0,
            'is_active' => $this->request->getPost('is_active') ?? 1,
            'license_status' => $this->request->getPost('license_status'),
            'updated_by' => session()->get('dakoii_user_id')
        ];
        
        if ($this->dakoiiOrgModel->update($id, $data)) {
            return redirect()->to('dakoii/organizations')->with('success', 'Organization updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update organization.');
        }
    }
    
    /**
     * Delete organization
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $organization = $this->dakoiiOrgModel->find($id);
        
        if (!$organization) {
            return redirect()->to('dakoii/organizations')->with('error', 'Organization not found.');
        }
        
        if ($this->dakoiiOrgModel->delete($id)) {
            return redirect()->to('dakoii/organizations')->with('success', 'Organization deleted successfully.');
        } else {
            return redirect()->to('dakoii/organizations')->with('error', 'Failed to delete organization.');
        }
    }
    
    /**
     * Get provinces by country (for form dropdowns)
     */
    public function getProvincesByCountry($countryId)
    {
        $provinces = $this->geoProvinceModel->getProvincesForDropdown($countryId);
        return $this->response->setJSON($provinces);
    }
}
