<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-eye me-2"></i>View Goods Item
                            </h2>
                            <p class="text-muted mb-0">Detailed information about this goods item</p>
                        </div>
                        <div>
                            <?= view('partials/back_button', [
                                'href' => base_url('admin/goods-items'),
                                'label' => 'Back to Items List',
                                'class' => 'btn btn-secondary me-2'
                            ]) ?>
                            <a href="<?= base_url('admin/goods-items/' . $item['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Information -->
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-info-circle me-2"></i>Item Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Item Name</label>
                                <div class="text-dark h5"><?= esc($item['item']) ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Goods Group</label>
                                <div>
                                    <span class="badge bg-secondary fs-6">
                                        <i class="bi bi-collection me-1"></i><?= esc($item['group_name']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Brand</label>
                                <div>
                                    <span class="badge bg-info fs-6">
                                        <i class="bi bi-tags me-1"></i><?= esc($item['brand_name']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <?php if ($item['status'] === 'active'): ?>
                                        <span class="badge bg-success fs-6">
                                            <i class="bi bi-check-circle me-1"></i>Active
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary fs-6">
                                            <i class="bi bi-pause-circle me-1"></i>Inactive
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($item['remarks'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Remarks</label>
                                    <div class="text-dark"><?= nl2br(esc($item['remarks'])) ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>


                </div>
            </div>

            <!-- Hierarchy Information -->
            <div class="card card-dark mt-4">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>Item Hierarchy
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="text-center">
                            <i class="bi bi-collection text-secondary" style="font-size: 2rem;"></i>
                            <div class="mt-2">
                                <strong class="text-dark"><?= esc($item['group_name']) ?></strong>
                                <br><small class="text-muted">Group</small>
                            </div>
                        </div>
                        <div class="mx-3">
                            <i class="bi bi-arrow-right text-muted"></i>
                        </div>
                        <div class="text-center">
                            <i class="bi bi-tags text-info" style="font-size: 2rem;"></i>
                            <div class="mt-2">
                                <strong class="text-dark"><?= esc($item['brand_name']) ?></strong>
                                <br><small class="text-muted">Brand</small>
                            </div>
                        </div>
                        <div class="mx-3">
                            <i class="bi bi-arrow-right text-muted"></i>
                        </div>
                        <div class="text-center">
                            <i class="bi bi-box text-primary" style="font-size: 2rem;"></i>
                            <div class="mt-2">
                                <strong class="text-primary"><?= esc($item['item']) ?></strong>
                                <br><small class="text-muted">Item</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metadata Sidebar -->
        <div class="col-lg-4">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-clock-history me-2"></i>Record Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div class="text-dark">
                            <?= date('M d, Y', strtotime($item['created_at'])) ?><br>
                            <small class="text-muted"><?= date('H:i:s', strtotime($item['created_at'])) ?></small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Last Updated</label>
                        <div class="text-dark">
                            <?= date('M d, Y', strtotime($item['updated_at'])) ?><br>
                            <small class="text-muted"><?= date('H:i:s', strtotime($item['updated_at'])) ?></small>
                        </div>
                    </div>

                    <?php if (!empty($item['status_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Changed</label>
                            <div class="text-dark">
                                <?= date('M d, Y', strtotime($item['status_at'])) ?><br>
                                <small class="text-muted"><?= date('H:i:s', strtotime($item['status_at'])) ?></small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card card-dark mt-4">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('admin/goods-items/' . $item['id'] . '/edit') ?>"
                           class="btn btn-warning">
                            <i class="bi bi-pencil me-2"></i>Edit Item
                        </a>
                        
                        <a href="<?= base_url('admin/goods-groups/' . $item['goods_group_id']) ?>"
                           class="btn btn-secondary">
                            <i class="bi bi-collection me-2"></i>View Group
                        </a>
                        
                        <a href="<?= base_url('admin/goods-brands/' . $item['goods_brand_id']) ?>"
                           class="btn btn-info">
                            <i class="bi bi-tags me-2"></i>View Brand
                        </a>
                        
                        <a href="<?= base_url('admin/goods-items?brand_id=' . $item['goods_brand_id']) ?>"
                           class="btn btn-outline-primary">
                            <i class="bi bi-box me-2"></i>Other Items in Brand
                        </a>
                        
                        <form method="post" action="<?= base_url('admin/goods-items/' . $item['id'] . '/delete') ?>"
                              onsubmit="return confirm('Are you sure you want to delete this goods item? This action cannot be undone.')">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="bi bi-trash me-2"></i>Delete Item
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
