<?php

namespace App\Models;

use CodeIgniter\Model;

class GeoProvinceModel extends Model
{
    protected $table = 'geo_provinces';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    
    protected $allowedFields = [
        'province_code',
        'name',
        'country_id',
        'json_id',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'province_code' => 'required|max_length[10]|is_unique[geo_provinces.province_code,id,{id}]',
        'name' => 'required|max_length[100]',
        'country_id' => 'required|integer'
    ];
    
    protected $validationMessages = [
        'province_code' => [
            'required' => 'Province code is required',
            'max_length' => 'Province code cannot exceed 10 characters',
            'is_unique' => 'Province code already exists'
        ],
        'name' => [
            'required' => 'Province name is required',
            'max_length' => 'Province name cannot exceed 100 characters'
        ],
        'country_id' => [
            'required' => 'Country is required',
            'integer' => 'Invalid country selection'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Get provinces by country
     */
    public function getByCountry(int $countryId)
    {
        return $this->where('country_id', $countryId)
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get provinces for dropdown by country
     */
    public function getProvincesForDropdown(int $countryId)
    {
        $provinces = $this->getByCountry($countryId);
        $dropdown = [];
        
        foreach ($provinces as $province) {
            $dropdown[$province['id']] = $province['name'];
        }
        
        return $dropdown;
    }
    
    /**
     * Get provinces with country info
     */
    public function getProvincesWithCountry()
    {
        return $this->select('geo_provinces.*, geo_countries.name as country_name, geo_countries.country_code')
                   ->join('geo_countries', 'geo_countries.id = geo_provinces.country_id', 'left')
                   ->orderBy('geo_countries.name', 'ASC')
                   ->orderBy('geo_provinces.name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get province by code
     */
    public function getByCode(string $provinceCode)
    {
        return $this->where('province_code', $provinceCode)->first();
    }
    
    /**
     * Search provinces
     */
    public function searchProvinces(string $keyword, int $countryId = null)
    {
        $builder = $this->like('name', $keyword)
                       ->orLike('province_code', $keyword);
        
        if ($countryId) {
            $builder->where('country_id', $countryId);
        }
        
        return $builder->orderBy('name', 'ASC')->findAll();
    }
}
