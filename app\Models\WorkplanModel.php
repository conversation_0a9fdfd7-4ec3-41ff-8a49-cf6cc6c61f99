<?php

namespace App\Models;

use CodeIgniter\Model;

class WorkplanModel extends Model
{
    protected $table = 'workplans';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'org_id',
        'supervisor_id',
        'date_from',
        'date_to',
        'title',
        'remarks',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'org_id' => 'required|integer',
        'supervisor_id' => 'required|integer',
        'date_from' => 'required|valid_date',
        'date_to' => 'required|valid_date',
        'title' => 'required|max_length[200]',
        'remarks' => 'permit_empty|max_length[65535]',
        'status' => 'required|in_list[active,inactive,completed,cancelled]',
        'status_by' => 'permit_empty|integer',
        'status_remarks' => 'permit_empty|max_length[65535]',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization is required',
            'integer' => 'Invalid organization selection'
        ],
        'supervisor_id' => [
            'required' => 'Supervisor is required',
            'integer' => 'Invalid supervisor selection'
        ],
        'date_from' => [
            'required' => 'Start date is required',
            'valid_date' => 'Invalid start date format'
        ],
        'date_to' => [
            'required' => 'End date is required',
            'valid_date' => 'Invalid end date format'
        ],
        'title' => [
            'required' => 'Title is required',
            'max_length' => 'Title cannot exceed 200 characters'
        ],
        'remarks' => [
            'max_length' => 'Remarks is too long'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be active, inactive, completed, or cancelled'
        ],
        'status_by' => [
            'integer' => 'Invalid user ID for status_by'
        ],
        'created_by' => [
            'integer' => 'Invalid user ID for created_by'
        ],
        'updated_by' => [
            'integer' => 'Invalid user ID for updated_by'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Custom validation to check date range
     */
    protected $beforeInsert = ['validateDateRange'];
    protected $beforeUpdate = ['validateDateRange'];
    
    protected function validateDateRange(array $data)
    {
        if (isset($data['data']['date_from']) && isset($data['data']['date_to'])) {
            $dateFrom = strtotime($data['data']['date_from']);
            $dateTo = strtotime($data['data']['date_to']);
            
            if ($dateFrom >= $dateTo) {
                throw new \CodeIgniter\Database\Exceptions\DatabaseException('End date must be after start date');
            }
        }
        
        return $data;
    }
    
    /**
     * Get workplans by organization
     */
    public function getWorkplansByOrg(int $orgId)
    {
        return $this->where('org_id', $orgId)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get active workplans by organization
     */
    public function getActiveWorkplansByOrg(int $orgId)
    {
        return $this->where('org_id', $orgId)
                   ->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get workplans by supervisor
     */
    public function getWorkplansBySupervisor(int $supervisorId)
    {
        return $this->where('supervisor_id', $supervisorId)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get workplans within date range
     */
    public function getWorkplansInDateRange(int $orgId, string $dateFrom, string $dateTo)
    {
        return $this->where('org_id', $orgId)
                   ->where('date_from >=', $dateFrom)
                   ->where('date_to <=', $dateTo)
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'ASC')
                   ->findAll();
    }
    
    /**
     * Update workplan status
     */
    public function updateStatus(int $workplanId, string $status, int $userId, string $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => $userId
        ];
        
        if ($remarks) {
            $data['status_remarks'] = $remarks;
        }
        
        return $this->update($workplanId, $data);
    }
    
    /**
     * Get workplan statistics by organization
     */
    public function getWorkplanStats(int $orgId)
    {
        $stats = [];
        
        // Total workplans
        $stats['total'] = $this->where('org_id', $orgId)
                              ->where('is_deleted', false)
                              ->countAllResults();
        
        // Active workplans
        $stats['active'] = $this->where('org_id', $orgId)
                               ->where('status', 'active')
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        // Completed workplans
        $stats['completed'] = $this->where('org_id', $orgId)
                                  ->where('status', 'completed')
                                  ->where('is_deleted', false)
                                  ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Search workplans
     */
    public function searchWorkplans(int $orgId, string $keyword)
    {
        return $this->where('org_id', $orgId)
                   ->groupStart()
                       ->like('title', $keyword)
                       ->orLike('remarks', $keyword)
                   ->groupEnd()
                   ->where('is_deleted', false)
                   ->orderBy('date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get workplan with details
     */
    public function getWorkplanWithDetails(int $workplanId)
    {
        return $this->where('id', $workplanId)
                   ->where('is_deleted', false)
                   ->first();
    }
    
    /**
     * Get active workplans for dropdown
     */
    public function getActiveWorkplansForDropdown(int $orgId)
    {
        $workplans = $this->getActiveWorkplansByOrg($orgId);
        
        $dropdown = [];
        foreach ($workplans as $workplan) {
            $dropdown[$workplan['id']] = $workplan['title'];
        }
        
        return $dropdown;
    }
}
