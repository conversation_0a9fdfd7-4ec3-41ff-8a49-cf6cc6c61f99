<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-calendar-check me-2"></i>Workplans Management
                            </h2>
                            <p class="text-muted mb-0">Manage and monitor workplans for your organization</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-house me-2"></i>Back to Dashboard
                            </a>
                            <a href="<?= base_url('admin/workplans/new') ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Create New Workplan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Workplans Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>All Workplans
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($workplans)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-calendar-x text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Workplans Found</h4>
                            <p class="text-muted">Get started by creating your first workplan.</p>
                            <a href="<?= base_url('admin/workplans/new') ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Create New Workplan
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Title</th>
                                        <th>Supervisor</th>
                                        <th>Date Range</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($workplans as $index => $workplan): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary"><?= $index + 1 ?></span>
                                            </td>
                                            <td>
                                                <strong class="text-dark"><?= esc($workplan['title']) ?></strong>
                                                <?php if (!empty($workplan['remarks'])): ?>
                                                    <br><small class="text-muted"><?= esc(substr($workplan['remarks'], 0, 50)) ?><?= strlen($workplan['remarks']) > 50 ? '...' : '' ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <i class="bi bi-person-badge me-1"></i>
                                                <?= esc($workplan['supervisor_name']) ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">From:</small><br>
                                                <span class="text-dark"><?= date('M d, Y', strtotime($workplan['date_from'])) ?></span><br>
                                                <small class="text-muted">To:</small><br>
                                                <span class="text-dark"><?= date('M d, Y', strtotime($workplan['date_to'])) ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusIcon = '';
                                                switch ($workplan['status']) {
                                                    case 'active':
                                                        $statusClass = 'bg-success';
                                                        $statusIcon = 'bi-check-circle';
                                                        break;
                                                    case 'inactive':
                                                        $statusClass = 'bg-secondary';
                                                        $statusIcon = 'bi-pause-circle';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-primary';
                                                        $statusIcon = 'bi-check-circle-fill';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusIcon = 'bi-x-circle';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-secondary';
                                                        $statusIcon = 'bi-question-circle';
                                                }
                                                ?>
                                                <span class="badge <?= $statusClass ?>">
                                                    <i class="<?= $statusIcon ?> me-1"></i><?= ucfirst($workplan['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($workplan['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('admin/workplans/' . $workplan['id']) ?>" 
                                                       class="btn btn-outline-info" 
                                                       title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/workplans/' . $workplan['id'] . '/edit') ?>" 
                                                       class="btn btn-outline-warning" 
                                                       title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger" 
                                                            title="Delete"
                                                            onclick="confirmDelete(<?= $workplan['id'] ?>, '<?= esc($workplan['title']) ?>')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light">
                <p>Are you sure you want to delete the workplan:</p>
                <p><strong id="workplanTitle"></strong></p>
                <p class="text-warning">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>Delete Workplan
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(workplanId, workplanTitle) {
    document.getElementById('workplanTitle').textContent = workplanTitle;
    document.getElementById('deleteForm').action = '<?= base_url('admin/workplans/') ?>' + workplanId + '/delete';
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>
