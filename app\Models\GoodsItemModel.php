<?php

namespace App\Models;

use CodeIgniter\Model;

class GoodsItemModel extends Model
{
    protected $table = 'goods_items';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'goods_group_id',
        'goods_brand_id',
        'item',
        'remarks',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'goods_group_id' => 'required|integer',
        'goods_brand_id' => 'required|integer',
        'item' => 'required|max_length[200]',
        'remarks' => 'permit_empty|max_length[65535]',
        'status' => 'required|in_list[active,inactive]',
        'status_by' => 'permit_empty|integer',
        'status_remarks' => 'permit_empty|max_length[65535]',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'goods_group_id' => [
            'required' => 'Goods group is required',
            'integer' => 'Invalid goods group selection'
        ],
        'goods_brand_id' => [
            'required' => 'Goods brand is required',
            'integer' => 'Invalid goods brand selection'
        ],
        'item' => [
            'required' => 'Item name is required',
            'max_length' => 'Item name cannot exceed 200 characters'
        ],
        'remarks' => [
            'max_length' => 'Remarks is too long'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be either active or inactive'
        ],
        'status_by' => [
            'integer' => 'Invalid user ID for status_by'
        ],
        'created_by' => [
            'integer' => 'Invalid user ID for created_by'
        ],
        'updated_by' => [
            'integer' => 'Invalid user ID for updated_by'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Custom validation to check unique item per brand and group
     */
    protected $beforeInsert = ['validateUniqueItemPerBrandGroup'];
    protected $beforeUpdate = ['validateUniqueItemPerBrandGroup'];
    
    protected function validateUniqueItemPerBrandGroup(array $data)
    {
        if (isset($data['data']['goods_group_id']) && isset($data['data']['goods_brand_id']) && isset($data['data']['item'])) {
            $builder = $this->where('goods_group_id', $data['data']['goods_group_id'])
                           ->where('goods_brand_id', $data['data']['goods_brand_id'])
                           ->where('item', $data['data']['item'])
                           ->where('is_deleted', false);
            
            // For updates, exclude current record
            if (isset($data['id'])) {
                $builder->where('id !=', $data['id']);
            }
            
            if ($builder->countAllResults() > 0) {
                throw new \CodeIgniter\Database\Exceptions\DatabaseException('Item already exists for this brand and group combination');
            }
        }
        
        return $data;
    }
    
    /**
     * Get items with group and brand information
     */
    public function getItemsWithDetails()
    {
        return $this->where('is_deleted', false)
                   ->orderBy('item', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get items by group
     */
    public function getItemsByGroup(int $groupId)
    {
        return $this->where('goods_group_id', $groupId)
                   ->where('is_deleted', false)
                   ->orderBy('item', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get items by brand
     */
    public function getItemsByBrand(int $brandId)
    {
        return $this->where('goods_brand_id', $brandId)
                   ->where('is_deleted', false)
                   ->orderBy('item', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get active items by brand
     */
    public function getActiveItemsByBrand(int $brandId)
    {
        return $this->where('goods_brand_id', $brandId)
                   ->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('item_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get active items for dropdown by brand
     */
    public function getActiveItemsForDropdownByBrand(int $brandId)
    {
        $items = $this->getActiveItemsByBrand($brandId);
        
        $dropdown = [];
        foreach ($items as $item) {
            $dropdown[$item['id']] = $item['item'];
        }
        
        return $dropdown;
    }
    
    /**
     * Search items
     */
    public function searchItems(string $keyword, int $groupId = null, int $brandId = null)
    {
        $builder = $this->select('goods_items.*, goods_groups.group_name, goods_brands.brand_name, goods_brands.type as brand_type')
                       ->join('goods_groups', 'goods_groups.id = goods_items.goods_group_id', 'left')
                       ->join('goods_brands', 'goods_brands.id = goods_items.goods_brand_id', 'left')
                       ->groupStart()
                           ->like('goods_items.item', $keyword)
                           ->orLike('goods_items.remarks', $keyword)
                           ->orLike('goods_groups.group_name', $keyword)
                           ->orLike('goods_brands.brand_name', $keyword)
                       ->groupEnd()
                       ->where('goods_items.is_deleted', false);
        
        if ($groupId) {
            $builder->where('goods_items.goods_group_id', $groupId);
        }
        
        if ($brandId) {
            $builder->where('goods_items.goods_brand_id', $brandId);
        }
        
        return $builder->orderBy('goods_groups.group_name', 'ASC')
                      ->orderBy('goods_brands.brand_name', 'ASC')
                      ->orderBy('goods_items.item_name', 'ASC')
                      ->findAll();
    }
    
    /**
     * Get item statistics
     */
    public function getItemStats()
    {
        $stats = [];
        
        // Total items
        $stats['total'] = $this->where('is_deleted', false)->countAllResults();
        
        // Active items
        $stats['active'] = $this->where('status', 'active')
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        // Inactive items
        $stats['inactive'] = $this->where('status', 'inactive')
                                 ->where('is_deleted', false)
                                 ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Update item status
     */
    public function updateStatus(int $itemId, string $status, int $userId, string $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => $userId
        ];
        
        if ($remarks) {
            $data['status_remarks'] = $remarks;
        }
        
        return $this->update($itemId, $data);
    }
    
    /**
     * Get items by group and brand combination
     */
    public function getItemsByGroupAndBrand(int $groupId, int $brandId)
    {
        return $this->where('goods_group_id', $groupId)
                   ->where('goods_brand_id', $brandId)
                   ->where('is_deleted', false)
                   ->orderBy('item_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get item with full details
     */
    public function getItemWithDetails(int $itemId)
    {
        return $this->where('id', $itemId)
                   ->where('is_deleted', false)
                   ->first();
    }
}
