<?php

namespace App\Models;

use CodeIgniter\Model;

class GeoCountryModel extends Model
{
    protected $table = 'geo_countries';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    
    protected $allowedFields = [
        'name',
        'country_code',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'country_code' => 'required|exact_length[2]|is_unique[geo_countries.country_code,id,{id}]'
    ];
    
    protected $validationMessages = [
        'name' => [
            'required' => 'Country name is required',
            'max_length' => 'Country name cannot exceed 100 characters'
        ],
        'country_code' => [
            'required' => 'Country code is required',
            'exact_length' => 'Country code must be exactly 2 characters',
            'is_unique' => 'Country code already exists'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Get all countries for dropdown
     */
    public function getCountriesForDropdown()
    {
        $countries = $this->orderBy('name', 'ASC')->findAll();
        $dropdown = [];
        
        foreach ($countries as $country) {
            $dropdown[$country['id']] = $country['name'];
        }
        
        return $dropdown;
    }
    
    /**
     * Get country by code
     */
    public function getByCode(string $countryCode)
    {
        return $this->where('country_code', $countryCode)->first();
    }
    
    /**
     * Get countries with province count
     */
    public function getCountriesWithProvinceCount()
    {
        return $this->select('geo_countries.*, COUNT(geo_provinces.id) as province_count')
                   ->join('geo_provinces', 'geo_provinces.country_id = geo_countries.id', 'left')
                   ->groupBy('geo_countries.id')
                   ->orderBy('geo_countries.name', 'ASC')
                   ->findAll();
    }
}
