<?php

namespace App\Models;

use CodeIgniter\Model;

class GoodsBrandModel extends Model
{
    protected $table = 'goods_brands';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'goods_group_id',
        'brand_name',
        'type',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'goods_group_id' => 'required|integer',
        'brand_name' => 'required|max_length[150]',
        'type' => 'required|in_list[primary,substitute]',
        'status' => 'required|in_list[active,inactive]',
        'status_by' => 'permit_empty|integer',
        'status_remarks' => 'permit_empty|max_length[65535]',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'goods_group_id' => [
            'required' => 'Goods group is required',
            'integer' => 'Invalid goods group selection'
        ],
        'brand_name' => [
            'required' => 'Brand name is required',
            'max_length' => 'Brand name cannot exceed 150 characters'
        ],
        'type' => [
            'required' => 'Brand type is required',
            'in_list' => 'Brand type must be either primary or substitute'
        ],
        'status' => [
            'required' => 'Status is required',
            'in_list' => 'Status must be either active or inactive'
        ],
        'status_by' => [
            'integer' => 'Invalid user ID for status_by'
        ],
        'created_by' => [
            'integer' => 'Invalid user ID for created_by'
        ],
        'updated_by' => [
            'integer' => 'Invalid user ID for updated_by'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Custom validation to check unique brand name per group
     */
    protected $beforeInsert = ['validateUniqueBrandPerGroup'];
    protected $beforeUpdate = ['validateUniqueBrandPerGroup'];
    
    protected function validateUniqueBrandPerGroup(array $data)
    {
        if (isset($data['data']['goods_group_id']) && isset($data['data']['brand_name'])) {
            $builder = $this->where('goods_group_id', $data['data']['goods_group_id'])
                           ->where('brand_name', $data['data']['brand_name'])
                           ->where('is_deleted', false);
            
            // For updates, exclude current record
            if (isset($data['id'])) {
                $builder->where('id !=', $data['id']);
            }
            
            if ($builder->countAllResults() > 0) {
                throw new \CodeIgniter\Database\Exceptions\DatabaseException('Brand name already exists in this goods group');
            }
        }
        
        return $data;
    }
    
    /**
     * Get active brands for dropdown by group
     */
    public function getActiveBrandsByGroup(int $groupId)
    {
        $brands = $this->where('goods_group_id', $groupId)
                      ->where('status', 'active')
                      ->where('is_deleted', false)
                      ->orderBy('brand_name', 'ASC')
                      ->findAll();
        
        $dropdown = [];
        foreach ($brands as $brand) {
            $dropdown[$brand['id']] = $brand['brand_name'];
        }
        
        return $dropdown;
    }
    
    /**
     * Get brands with group information and item count using simple operations
     */
    public function getBrandsWithGroup()
    {
        $brands = $this->where('is_deleted', false)
                      ->orderBy('brand_name', 'ASC')
                      ->findAll();

        // Add group information and item count using simple operations
        $goodsGroupModel = new \App\Models\GoodsGroupModel();
        $goodsItemModel = new \App\Models\GoodsItemModel();

        foreach ($brands as &$brand) {
            // Get group name
            $group = $goodsGroupModel->find($brand['goods_group_id']);
            $brand['group_name'] = $group ? $group['group_name'] : 'Unknown';

            // Get item count
            $brand['item_count'] = $goodsItemModel->where('goods_brand_id', $brand['id'])
                                                 ->where('is_deleted', false)
                                                 ->countAllResults();
        }

        return $brands;
    }
    
    /**
     * Get brands by group with item count using simple operations
     */
    public function getBrandsByGroupWithItemCount(int $groupId)
    {
        $brands = $this->where('goods_group_id', $groupId)
                      ->where('is_deleted', false)
                      ->orderBy('brand_name', 'ASC')
                      ->findAll();

        // Add group information and item count using simple operations
        $goodsGroupModel = new \App\Models\GoodsGroupModel();
        $goodsItemModel = new \App\Models\GoodsItemModel();

        foreach ($brands as &$brand) {
            // Get group name
            $group = $goodsGroupModel->find($brand['goods_group_id']);
            $brand['group_name'] = $group ? $group['group_name'] : 'Unknown';

            // Get item count
            $brand['item_count'] = $goodsItemModel->where('goods_brand_id', $brand['id'])
                                                 ->where('is_deleted', false)
                                                 ->countAllResults();
        }

        return $brands;
    }
    
    /**
     * Get active brands by group
     */
    public function getActiveBrandsByGroupId(int $groupId)
    {
        return $this->where('goods_group_id', $groupId)
                   ->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('brand_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get primary brands by group
     */
    public function getPrimaryBrandsByGroup(int $groupId)
    {
        return $this->where('goods_group_id', $groupId)
                   ->where('type', 'primary')
                   ->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('brand_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get substitute brands by group
     */
    public function getSubstituteBrandsByGroup(int $groupId)
    {
        return $this->where('goods_group_id', $groupId)
                   ->where('type', 'substitute')
                   ->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('brand_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Search brands
     */
    public function searchBrands(string $keyword, int $groupId = null)
    {
        $builder = $this->select('goods_brands.*, goods_groups.group_name')
                       ->join('goods_groups', 'goods_groups.id = goods_brands.goods_group_id', 'left')
                       ->groupStart()
                           ->like('goods_brands.brand_name', $keyword)
                           ->orLike('goods_groups.group_name', $keyword)
                       ->groupEnd()
                       ->where('goods_brands.is_deleted', false);
        
        if ($groupId) {
            $builder->where('goods_brands.goods_group_id', $groupId);
        }
        
        return $builder->orderBy('goods_groups.group_name', 'ASC')
                      ->orderBy('goods_brands.brand_name', 'ASC')
                      ->findAll();
    }
    
    /**
     * Get brand statistics
     */
    public function getBrandStats()
    {
        $stats = [];
        
        // Total brands
        $stats['total'] = $this->where('is_deleted', false)->countAllResults();
        
        // Active brands
        $stats['active'] = $this->where('status', 'active')
                               ->where('is_deleted', false)
                               ->countAllResults();
        
        // Primary brands
        $stats['primary'] = $this->where('type', 'primary')
                                ->where('is_deleted', false)
                                ->countAllResults();
        
        // Substitute brands
        $stats['substitute'] = $this->where('type', 'substitute')
                                   ->where('is_deleted', false)
                                   ->countAllResults();
        
        return $stats;
    }
    
    /**
     * Update brand status
     */
    public function updateStatus(int $brandId, string $status, int $userId, string $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => $userId,
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => $userId
        ];
        
        if ($remarks) {
            $data['status_remarks'] = $remarks;
        }
        
        return $this->update($brandId, $data);
    }
    
    /**
     * Check if brand has items
     */
    public function hasItems(int $brandId): bool
    {
        $itemModel = new \App\Models\GoodsItemModel();
        return $itemModel->where('goods_brand_id', $brandId)
                        ->where('is_deleted', false)
                        ->countAllResults() > 0;
    }
}
