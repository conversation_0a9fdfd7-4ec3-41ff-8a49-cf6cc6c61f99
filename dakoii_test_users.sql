-- Test users for Dakoii Panel
-- Insert these records into the dakoii_users table

INSERT INTO `dakoii_users` (`name`, `username`, `password`, `orgcode`, `role`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
('Super Administrator', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'DAKOII-MAIN', 'Super Admin', 1, 1, NOW(), NOW()),
('System Administrator', 'sysadmin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'DAKOII-MAIN', 'System Admin', 1, 1, NOW(), NOW()),
('Test Manager', 'manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'DAKOII-TEST', 'Manager', 1, 1, NOW(), NOW());

-- Default password for all test users is: password
-- You should change these passwords after first login

-- Test credentials:
-- Username: admin, Password: password
-- Username: sysadmin, Password: password  
-- Username: manager, Password: password
