CREATE TABLE workplans (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    org_id INTEGER NOT NULL,
    supervisor_id INTEGER NOT NULL,
    date_from DATETIME NOT NULL,
    date_to DATETIME NOT NULL,
    title VARCHAR(200) NOT NULL,
    remarks TEXT,

    status VARCHAR(20) NOT NULL DEFAULT 'active',
    status_by INTEGER,
    status_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status_remarks TEXT,

    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_by INTEGER,
    deleted_at DATETIME
);


CREATE TABLE activities (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    org_id INTEGER NOT NULL,
    workplan_id INTEGER NOT NULL,
    activity_type VARCHAR(50) NOT NULL,                -- e.g., price_collection
    activity_name VARCHAR(200) NOT NULL,
    date_from DATETIME NOT NULL,
    date_to DATETIME NOT NULL,
    remarks TEXT,

    status VARCHAR(20) NOT NULL DEFAULT 'active',      -- active, submitted, approved, redo
    status_by INTEGER,
    status_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status_remarks TEXT,

    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_by INTEGER,
    deleted_at DATETIME
);


CREATE TABLE activity_users (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    org_id INTEGER NOT NULL,
    activity_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,

    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE activity_business_locations (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    org_id INTEGER NOT NULL,
    activity_id INTEGER NOT NULL,
    business_location_id INTEGER NOT NULL,

    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE activity_price_collection_data (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    org_id INTEGER NOT NULL,
    business_location_id INTEGER NOT NULL,
    activity_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    item_id INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    remarks TEXT,

    status VARCHAR(20) DEFAULT 'active',            -- e.g., active, submitted, approved, redo
    status_by INTEGER,
    status_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status_remarks TEXT,

    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_by INTEGER,
    deleted_at DATETIME
);
