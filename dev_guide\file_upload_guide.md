# File Upload Feature Guide - PCOLLX User Management

## Overview

This guide documents the file upload implementation for user ID photos in the PCOLLX User Management system. The feature allows administrators to upload and manage user ID photos through a secure, web-accessible file upload system.

## Architecture

### File Storage Structure

```
public/
├── index.php
├── assets/
└── uploads/           ← Physical file storage location
    └── id_photos/
        ├── abc123def456.jpg
        ├── xyz789uvw012.png
        └── ...
```

### Database Storage

- **Column**: `users.id_photo` (VARCHAR 500)
- **Format**: `public/uploads/id_photos/filename.ext`
- **Purpose**: Web-accessible path for direct URL generation

### Path Logic Separation

| Context | Path Format | Example |
|---------|-------------|---------|
| **Physical Storage** | `FCPATH . 'uploads/id_photos/'` | `/xampp/htdocs/pcollx/public/uploads/id_photos/` |
| **Database Storage** | `'public/uploads/id_photos/' . $filename` | `public/uploads/id_photos/abc123.jpg` |
| **Web Access** | `base_url($user['id_photo'])` | `http://localhost/pcollx/public/uploads/id_photos/abc123.jpg` |

## Implementation Details

### 1. Form Configuration

#### Create Form (`users_create.php`)
```html
<form method="post" action="<?= base_url('dakoii/system-users/create') ?>" enctype="multipart/form-data">
    <input type="file" class="form-control" id="id_photo" name="id_photo" accept="image/*">
    <div class="form-text">Upload user's ID photo (optional). Accepted formats: JPG, PNG, GIF</div>
</form>
```

#### Edit Form (`users_edit.php`)
```html
<form method="post" action="<?= base_url('dakoii/system-users/' . $user['id'] . '/update') ?>" enctype="multipart/form-data">
    <input type="file" class="form-control" id="id_photo" name="id_photo" accept="image/*">
    <div class="form-text">Upload new ID photo (optional). Accepted formats: JPG, PNG, GIF</div>
    <?php if (!empty($user['id_photo'])): ?>
        <div class="mt-2">
            <small class="text-muted">Current: <?= esc(basename($user['id_photo'])) ?></small>
        </div>
    <?php endif; ?>
</form>
```

### 2. Validation Rules

```php
'id_photo' => 'permit_empty|uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png,gif]'
```

**Validation Parameters:**
- `permit_empty`: File upload is optional
- `uploaded[id_photo]`: Validates proper file upload
- `max_size[id_photo,2048]`: Maximum 2MB file size
- `ext_in[id_photo,jpg,jpeg,png,gif]`: Allowed file extensions

### 3. Controller Logic

#### Create Method
```php
// Handle file upload for ID photo
$idPhotoPath = null;
$idPhotoFile = $this->request->getFile('id_photo');
if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
    // Ensure upload directory exists
    $uploadPath = FCPATH . 'uploads/id_photos/';
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }
    
    $newName = $idPhotoFile->getRandomName();
    $idPhotoFile->move($uploadPath, $newName);
    $idPhotoPath = 'public/uploads/id_photos/' . $newName; // Add public/ prefix for web access
}

$userData = [
    // ... other fields
    'id_photo' => $idPhotoPath,
    // ... other fields
];
```

#### Update Method
```php
// Handle file upload for ID photo
$idPhotoPath = $user['id_photo']; // Keep existing photo by default
$idPhotoFile = $this->request->getFile('id_photo');
if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
    // Ensure upload directory exists
    $uploadPath = FCPATH . 'uploads/id_photos/';
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }
    
    // Delete old photo if it exists (remove public/ prefix for file system access)
    if (!empty($user['id_photo']) && file_exists(FCPATH . str_replace('public/', '', $user['id_photo']))) {
        unlink(FCPATH . str_replace('public/', '', $user['id_photo']));
    }
    
    $newName = $idPhotoFile->getRandomName();
    $idPhotoFile->move($uploadPath, $newName);
    $idPhotoPath = 'public/uploads/id_photos/' . $newName; // Add public/ prefix for web access
}

$userData = [
    // ... other fields
    'id_photo' => $idPhotoPath,
    // ... other fields
];
```

## View Integration

### 1. User Details View (`users_show.php`)

```html
<div class="col-md-6 mb-3">
    <label class="form-label text-muted">ID Photo</label>
    <div class="fw-bold">
        <?php if ($user['id_photo']): ?>
            <div class="mb-2">
                <img src="<?= base_url($user['id_photo']) ?>" alt="ID Photo" 
                     class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
            </div>
            <small class="text-muted"><?= esc(basename($user['id_photo'])) ?></small>
        <?php else: ?>
            <span class="text-muted">Not provided</span>
        <?php endif; ?>
    </div>
</div>
```

### 2. User List View (`users_index.php`)

```html
<div class="d-flex align-items-center">
    <?php if ($user['id_photo']): ?>
        <img src="<?= base_url($user['id_photo']) ?>" alt="<?= esc($user['name']) ?>" 
             class="avatar-sm rounded-circle me-2" 
             style="width: 40px; height: 40px; object-fit: cover;">
    <?php else: ?>
        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
            <i class="bi bi-person text-white"></i>
        </div>
    <?php endif; ?>
    <div>
        <strong><?= esc($user['name']) ?></strong>
        <?php if ($user['position']): ?>
            <br><small class="text-muted"><?= esc($user['position']) ?></small>
        <?php endif; ?>
    </div>
</div>
```

## Security Features

### 1. File Validation
- **Type Checking**: Only image files (JPG, JPEG, PNG, GIF) allowed
- **Size Limits**: Maximum 2MB to prevent abuse
- **Upload Validation**: Checks if file is valid upload
- **Extension Whitelist**: Prevents execution of malicious files

### 2. File Storage Security
- **Random Naming**: Uses `getRandomName()` to prevent conflicts and guessing
- **Secure Location**: Files stored in web-accessible but controlled directory
- **Permission Control**: Directory created with 0755 permissions
- **Old File Cleanup**: Previous files deleted when replaced

### 3. Path Security
- **Separation of Concerns**: Physical paths separate from web paths
- **Input Sanitization**: File paths properly escaped in views
- **Direct Access**: Files served directly by web server (no PHP processing)

## Troubleshooting

### Common Issues

1. **Directory Permission Errors**
   - Ensure web server has write permissions to `public/uploads/`
   - Check directory is created with proper permissions (0755)

2. **File Not Found Errors**
   - Verify `public/uploads/id_photos/` directory exists
   - Check file paths in database match actual file locations

3. **Upload Size Errors**
   - Check PHP `upload_max_filesize` and `post_max_size` settings
   - Verify CodeIgniter validation rules match PHP limits

4. **Image Display Issues**
   - Ensure `base_url()` is properly configured
   - Check file paths in database include `public/` prefix

### File Path Debugging

```php
// Debug file paths
echo "Physical path: " . FCPATH . 'uploads/id_photos/' . $filename . "\n";
echo "Database path: " . 'public/uploads/id_photos/' . $filename . "\n";
echo "Web URL: " . base_url('public/uploads/id_photos/' . $filename) . "\n";
```

## Best Practices

1. **Always validate file uploads** before processing
2. **Use random filenames** to prevent conflicts and security issues
3. **Clean up old files** when replacing to save disk space
4. **Separate physical and web paths** for better maintainability
5. **Set appropriate file size limits** based on requirements
6. **Use proper image optimization** for better performance
7. **Implement proper error handling** for upload failures

## Future Enhancements

- Image resizing/optimization on upload
- Multiple file format support
- Bulk upload functionality
- Image cropping interface
- CDN integration for better performance
- Watermarking for security
