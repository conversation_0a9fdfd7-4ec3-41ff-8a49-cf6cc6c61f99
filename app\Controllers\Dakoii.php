<?php

namespace App\Controllers;

use App\Models\DakoiiUserModel;
use App\Models\DakoiiOrgModel;
use App\Models\UserModel;
use App\Models\GeoCountryModel;
use App\Models\GeoProvinceModel;

class <PERSON>koii extends BaseController
{
    protected $dakoiiUserModel;
    protected $dakoiiOrgModel;
    protected $userModel;
    protected $geoCountryModel;
    protected $geoProvinceModel;

    public function __construct()
    {
        $this->dakoiiUserModel = new DakoiiUserModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        $this->userModel = new UserModel();
        $this->geoCountryModel = new GeoCountryModel();
        $this->geoProvinceModel = new GeoProvinceModel();

        // Load helper
        helper('dakoii');
    }

    /**
     * Display login page
     */
    public function index()
    {
        // Check if user is already logged in
        if (session()->get('dakoii_logged_in')) {
            return redirect()->to('dakoii/dashboard');
        }

        $data = [
            'title' => 'Dakoii Panel Login'
        ];

        return view('dakoii/login', $data);
    }

    /**
     * Handle login authentication
     */
    public function authenticate()
    {
        // Check if request method is POST
        if (!$this->request->is('post')) {
            return redirect()->to('dakoii')->with('error', 'Invalid request method.');
        }

        // Get form data
        $username = trim($this->request->getPost('username'));
        $password = $this->request->getPost('password');
        $remember = $this->request->getPost('remember');

        // Basic validation
        if (empty($username) || empty($password)) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Please enter both username and password.');
        }

        // Verify credentials
        $user = $this->dakoiiUserModel->verifyCredentials($username, $password);

        if ($user) {
            // Set session data
            $sessionData = [
                'dakoii_user_id' => $user['id'],
                'dakoii_username' => $user['username'],
                'dakoii_name' => $user['name'],
                'dakoii_role' => $user['role'] ?? 'User',
                'dakoii_orgcode' => $user['orgcode'] ?? 'N/A',
                'dakoii_logged_in' => true
            ];

            session()->set($sessionData);

            // Handle remember me functionality
            if ($remember) {
                // Set a longer session expiration (30 days)
                session()->setTempdata('dakoii_remember', true, 30 * 24 * 60 * 60);
            }

            // Update user activity
            $this->dakoiiUserModel->updateActivity($user['id']);

            // Redirect to dashboard
            return redirect()->to('dakoii/dashboard')->with('success', 'Welcome back, ' . $user['name'] . '!');
        } else {
            // Authentication failed
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Invalid username or password. Please try again.');
        }
    }

    /**
     * Display dashboard
     */
    public function dashboard()
    {
        // Check if user is logged in
        if (!session()->get('dakoii_logged_in')) {
            return redirect()->to('dakoii')->with('error', 'Please login to access the dashboard.');
        }

        // Get statistics
        $orgStats = $this->dakoiiOrgModel->getOrgStats();
        $userStats = $this->userModel->getUserStats();

        // Get recent organizations (last 5)
        $recentOrgs = $this->dakoiiOrgModel->orderBy('created_at', 'DESC')->limit(5)->find();

        // Get recent users (last 5)
        $recentUsers = $this->userModel->select('users.*, dakoii_org.org_name')
                                      ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                                      ->where('users.deleted_at', null)
                                      ->orderBy('users.created_at', 'DESC')
                                      ->limit(5)
                                      ->find();

        $data = [
            'title' => 'Dakoii Panel Dashboard',
            'user' => [
                'id' => session()->get('dakoii_user_id'),
                'username' => session()->get('dakoii_username'),
                'name' => session()->get('dakoii_name'),
                'role' => session()->get('dakoii_role'),
                'orgcode' => session()->get('dakoii_orgcode')
            ],
            'stats' => [
                'total_orgs' => $orgStats['total'],
                'active_orgs' => $orgStats['active'],
                'inactive_orgs' => $orgStats['inactive'],
                'total_users' => $userStats['total'],
                'active_users' => $userStats['active'],
                'admin_users' => $userStats['role_admin'] ?? 0,
                'supervisor_users' => $userStats['role_supervisor'] ?? 0
            ],
            'recent_orgs' => $recentOrgs,
            'recent_users' => $recentUsers
        ];

        return view('dakoii/dakoii_dashboard', $data);
    }

    /**
     * Handle logout
     */
    public function logout()
    {
        // Destroy session data
        session()->remove([
            'dakoii_user_id',
            'dakoii_username',
            'dakoii_name',
            'dakoii_role',
            'dakoii_orgcode',
            'dakoii_logged_in'
        ]);

        // Destroy the entire session
        session()->destroy();

        return redirect()->to('dakoii')->with('success', 'You have been logged out successfully.');
    }
}
