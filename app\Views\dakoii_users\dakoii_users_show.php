<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-person-circle me-2"></i>Admin User Details
                            </h2>
                            <p class="text-light mb-0">View administrator account information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                            <a href="<?= base_url('dakoii/users/' . $user['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-person-gear me-2"></i><?= esc($user['name']) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- User Avatar and Basic Info -->
                        <div class="col-md-4 text-center mb-4">
                            <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                                <i class="bi bi-person text-white" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="text-light"><?= esc($user['name']) ?></h4>
                            <p class="text-muted">@<?= esc($user['username']) ?></p>
                            
                            <?php
                            $roleColors = [
                                'super_admin' => 'danger',
                                'admin' => 'warning',
                                'moderator' => 'info'
                            ];
                            $color = $roleColors[$user['role']] ?? 'secondary';
                            ?>
                            <span class="badge bg-<?= $color ?> fs-6"><?= ucwords(str_replace('_', ' ', $user['role'])) ?></span>
                            
                            <div class="mt-3">
                                <?php if ($user['is_active']): ?>
                                    <span class="badge bg-success fs-6">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary fs-6">Inactive</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- User Information -->
                        <div class="col-md-8">
                            <h6 class="text-light mb-3">Account Information</h6>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">User ID:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="text-muted"><?= esc($user['id']) ?></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Username:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="text-muted"><?= esc($user['username']) ?></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Full Name:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="text-muted"><?= esc($user['name']) ?></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Role:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-<?= $color ?>"><?= ucwords(str_replace('_', ' ', $user['role'])) ?></span>
                                </div>
                            </div>



                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Status:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <?php if ($user['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Created:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="text-muted"><?= date('M d, Y H:i', strtotime($user['created_at'])) ?></span>
                                </div>
                            </div>

                            <?php if ($user['updated_at']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong class="text-light">Last Updated:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-muted"><?= date('M d, Y H:i', strtotime($user['updated_at'])) ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>


                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="<?= base_url('dakoii/users') ?>" class="btn btn-secondary">
                                    <i class="bi bi-list me-2"></i>Back to List
                                </a>
                                <a href="<?= base_url('dakoii/users/' . $user['id'] . '/edit') ?>" class="btn btn-warning">
                                    <i class="bi bi-pencil me-2"></i>Edit User
                                </a>
                                <?php if ($user['id'] != session()->get('dakoii_user_id')): ?>
                                    <form method="post" action="<?= base_url('dakoii/users/' . $user['id'] . '/delete') ?>" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this admin user?')">
                                        <?= csrf_field() ?>
                                        <button type="submit" class="btn btn-danger">
                                            <i class="bi bi-trash me-2"></i>Delete User
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 120px;
    height: 120px;
}
</style>
<?= $this->endSection() ?>
