<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class DakoiiUserSeeder extends Seeder
{
    public function run()
    {
        $data = [
            [
                'name' => 'Super Administrator',
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'orgcode' => 'DAKOII-MAIN',
                'role' => 'Super Admin',
                'is_active' => 1,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'System Administrator',
                'username' => 'sysadmin',
                'password' => password_hash('sysadmin123', PASSWORD_DEFAULT),
                'orgcode' => 'DAKOII-MAIN',
                'role' => 'System Admin',
                'is_active' => 1,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Test Manager',
                'username' => 'manager',
                'password' => password_hash('manager123', PASSWORD_DEFAULT),
                'orgcode' => 'DAKOII-TEST',
                'role' => 'Manager',
                'is_active' => 1,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert data
        $this->db->table('dakoii_users')->insertBatch($data);
    }
}
