<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-file-earmark-spreadsheet me-2"></i><?= $title ?>
            </h1>
            <p class="text-muted mb-0">Comprehensive report of all business entities and their locations</p>
        </div>
        <div>
            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary me-2">
                <i class="bi bi-house me-1"></i>Back to Dashboard
            </a>
            <button id="exportExcel" class="btn btn-success">
                <i class="bi bi-file-earmark-excel me-1"></i>Export to Excel
            </button>
        </div>
    </div>

    <!-- Report Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="bi bi-table me-2"></i>Business Entities & Locations Report
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($reportData)): ?>
                <div class="table-responsive">
                    <table id="businessEntitiesReportTable" class="table table-striped table-hover" style="background-color: #f8f9fa;">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th width="20%">Business Location</th>
                                <th width="20%">Business Entity</th>
                                <th width="12%">Country</th>
                                <th width="12%">Province</th>
                                <th width="12%">District</th>
                                <th width="14%">GPS Coordinates</th>
                                <th width="5%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reportData as $index => $data): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-info"><?= $index + 1 ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= esc($data['location_name']) ?></strong>
                                            <br><small class="text-muted">
                                                Status: 
                                                <?php
                                                $statusClass = $data['location_status'] === 'active' ? 'success' : 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $statusClass ?>"><?= ucfirst(esc($data['location_status'])) ?></span>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= esc($data['entity_name']) ?></strong>
                                            <br><small class="text-muted">
                                                Status: 
                                                <?php
                                                $entityStatusClass = $data['entity_status'] === 'active' ? 'success' : 'secondary';
                                                ?>
                                                <span class="badge bg-<?= $entityStatusClass ?>"><?= ucfirst(esc($data['entity_status'])) ?></span>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <?= $data['country'] ? esc($data['country']) : '<span class="text-muted">-</span>' ?>
                                    </td>
                                    <td>
                                        <?= $data['province'] ? esc($data['province']) : '<span class="text-muted">-</span>' ?>
                                    </td>
                                    <td>
                                        <?= $data['district'] ? esc($data['district']) : '<span class="text-muted">-</span>' ?>
                                    </td>
                                    <td>
                                        <?php if ($data['gps_coordinates']): ?>
                                            <div>
                                                <small class="text-muted"><?= esc($data['gps_coordinates']) ?></small>
                                                <br>
                                                <a href="https://www.google.com/maps?q=<?= urlencode($data['gps_coordinates']) ?>" 
                                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-geo-alt"></i>
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical" role="group">
                                            <a href="<?= base_url('admin/business-entities/' . $data['entity_id']) ?>" 
                                               class="btn btn-sm btn-outline-primary mb-1" title="Manage Entity">
                                                <i class="bi bi-building"></i>
                                            </a>
                                            <a href="<?= base_url('admin/business-locations/' . $data['location_id']) ?>" 
                                               class="btn btn-sm btn-outline-info" title="Manage Location">
                                                <i class="bi bi-geo-alt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-file-earmark-spreadsheet display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">No Data Available</h4>
                    <p class="text-muted">No business entities or locations found for the report.</p>
                    <a href="<?= base_url('admin/business-entities/new') ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Add Business Entity
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#businessEntitiesReportTable').DataTable({
        "paging": false, // No pagination as requested
        "searching": true,
        "ordering": true,
        "info": true,
        "responsive": true,
        "dom": 'Bfrtip',
        "buttons": [
            {
                extend: 'excel',
                text: '<i class="bi bi-file-earmark-excel me-1"></i>Excel',
                className: 'btn btn-success btn-sm d-none', // Hidden, triggered by custom button
                filename: 'Business_Entities_Report_' + new Date().toISOString().slice(0,10),
                title: 'Business Entities Report',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6] // Exclude Actions column
                }
            }
        ],
        "language": {
            "search": "Search report:",
            "info": "Showing _TOTAL_ business locations",
            "infoEmpty": "No business locations available",
            "infoFiltered": "(filtered from _MAX_ total locations)",
            "zeroRecords": "No matching business locations found"
        },
        "columnDefs": [
            { "orderable": false, "targets": 7 }, // Disable sorting on Actions column
            { "className": "text-center", "targets": [0, 7] } // Center align # and Actions columns
        ],
        "order": [[ 2, "asc" ], [ 1, "asc" ]] // Sort by Entity name, then Location name
    });

    // Custom export button
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });
});
</script>
<?= $this->endSection() ?>
