<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-people me-2"></i>Organization Users Management
                            </h2>
                            <p class="text-light mb-0">Manage users within client organizations</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?= base_url('dakoii/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-house me-1"></i> Back to Dashboard
                            </a>
                            <a href="<?= base_url('dakoii/organization-users/new') ?>" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>Add New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-table me-2"></i>Organization Users List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Organization</th>
                                        <th>Role</th>
                                        <th>Position</th>
                                        <th>Status</th>
                                        <th>Admin</th>
                                        <th>Supervisor</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><?= $user['id'] ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-person text-white"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold"><?= esc($user['name']) ?></div>
                                                        <small class="text-muted"><?= esc($user['phone']) ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?= esc($user['email']) ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= esc($user['org_name'] ?? 'N/A') ?>
                                                </span>
                                                <br>
                                                <small class="text-muted"><?= esc($user['org_code'] ?? 'N/A') ?></small>
                                            </td>
                                            <td>
                                                <span class="badge <?= $user['role'] == 'user' ? 'bg-success' : 'bg-secondary' ?>">
                                                    <?= ucfirst($user['role']) ?>
                                                </span>
                                            </td>
                                            <td><?= esc($user['position'] ?? 'N/A') ?></td>
                                            <td>
                                                <?php
                                                $statusClass = match($user['status']) {
                                                    'active' => 'bg-success',
                                                    'inactive' => 'bg-warning',
                                                    'suspended' => 'bg-danger',
                                                    default => 'bg-secondary'
                                                };
                                                ?>
                                                <span class="badge <?= $statusClass ?>">
                                                    <?= ucfirst($user['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($user['is_admin']): ?>
                                                    <i class="bi bi-shield-check text-warning" title="Admin"></i>
                                                <?else: ?>
                                                    <i class="bi bi-dash text-muted"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['is_supervisor']): ?>
                                                    <i class="bi bi-person-badge text-info" title="Supervisor"></i>
                                                <?else: ?>
                                                    <i class="bi bi-dash text-muted"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M j, Y', strtotime($user['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('dakoii/organization-users/' . $user['id']) ?>" 
                                                       class="btn btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('dakoii/organization-users/' . $user['id'] . '/edit') ?>" 
                                                       class="btn btn-outline-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <form method="post" 
                                                          action="<?= base_url('dakoii/organization-users/' . $user['id'] . '/delete') ?>" 
                                                          class="d-inline" 
                                                          onsubmit="return confirm('Are you sure you want to delete this user?')">
                                                        <?= csrf_field() ?>
                                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">No Organization Users Found</h4>
                            <p class="text-muted">Start by adding your first organization user.</p>
                            <a href="<?= base_url('dakoii/organization-users/new') ?>" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>Add New User
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
}
</style>
<?= $this->endSection() ?>
