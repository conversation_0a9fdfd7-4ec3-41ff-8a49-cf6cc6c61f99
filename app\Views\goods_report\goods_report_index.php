<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    /* Scoped to goods report page for future tweaks */
    .goods-report .card.card-dark .card-title {
        /* reserved for minor tuning to match theme */
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4 goods-report">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>Goods Report
                            </h2>
                            <p class="text-muted mb-0">Individual items with their groups and brands</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success" id="exportExcel">
                                <i class="bi bi-file-earmark-excel me-1"></i>Export Excel
                            </button>
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-house me-1"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Goods Items Report
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="goodsReportTable" class="table table-hover table-light">
                            <thead class="table-dark">
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="25%">Item</th>
                                    <th width="20%">Brand</th>
                                    <th width="15%">Type</th>
                                    <th width="20%">Group</th>
                                    <th width="15%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($items)): ?>
                                    <?php foreach ($items as $index => $item): ?>
                                        <tr>
                                            <td class="text-dark"><?= $index + 1 ?></td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="fw-bold text-dark"><?= esc($item['item']) ?></span>
                                                    <small class="text-muted">
                                                        Status: <span class="badge bg-<?= $item['status'] == 'active' ? 'success' : 'secondary' ?> badge-sm"><?= ucfirst($item['status']) ?></span>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-dark"><?= esc($item['brand_name']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $item['brand_type'] == 'primary' ? 'primary' : 'info' ?> fs-6"><?= ucfirst($item['brand_type']) ?></span>
                                            </td>
                                            <td>
                                                <span class="text-dark"><?= esc($item['group_name']) ?></span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('admin/goods-groups/' . $item['goods_group_id']) ?>"
                                                       class="btn btn-outline-success" title="Manage Group">
                                                        <i class="bi bi-collection"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/goods-brands/' . $item['goods_brand_id']) ?>"
                                                       class="btn btn-outline-warning" title="Manage Brand">
                                                        <i class="bi bi-tags"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/goods-items/' . $item['id']) ?>"
                                                       class="btn btn-outline-info" title="Manage Item">
                                                        <i class="bi bi-box"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4 d-block mb-3"></i>
                                                <h6>No Goods Items Found</h6>
                                                <p class="mb-0">Start by creating your first goods item.</p>
                                                <a href="<?= base_url('admin/goods-items/new') ?>" class="btn btn-primary mt-2">
                                                    <i class="bi bi-plus-circle me-1"></i>
                                                    Create Goods Item
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- jQuery (required for DataTables) -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#goodsReportTable').DataTable({
        "paging": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "responsive": true,
        "dom": 'Bfrtip',
        "buttons": [
            {
                extend: 'excel',
                text: '<i class="bi bi-file-earmark-excel me-1"></i>Export Excel',
                className: 'btn btn-success d-none',
                filename: 'Goods_Report_' + new Date().toISOString().slice(0,10),
                title: 'Goods Report - ' + new Date().toLocaleDateString(),
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Export only first 5 columns (exclude actions)
                }
            }
        ],
        "language": {
            "search": "Search items:",
            "info": "Showing _TOTAL_ goods items",
            "infoEmpty": "No goods items available",
            "infoFiltered": "(filtered from _MAX_ total items)",
            "zeroRecords": "No matching goods items found"
        },
        "columnDefs": [
            { "orderable": false, "targets": 5 }, // Disable sorting on Actions column
            { "className": "text-center", "targets": [0, 3] } // Center align # and Type columns
        ],
        "order": [[ 4, "asc" ], [ 2, "asc" ], [ 3, "asc" ], [ 1, "asc" ]] // Sort by Group, then Brand, then Type, then Item
    });

    // Custom export button
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    // Simple manual Excel export fallback
    $('#exportExcel').on('click', function() {
        if (table.button) {
            table.button('.buttons-excel').trigger();
        } else {
            // Fallback: simple table to CSV
            exportTableToCSV('goods_report.csv');
        }
    });

    function exportTableToCSV(filename) {
        var csv = [];
        var rows = document.querySelectorAll("#goodsReportTable tr");

        for (var i = 0; i < rows.length; i++) {
            var row = [], cols = rows[i].querySelectorAll("td, th");

            for (var j = 0; j < cols.length - 1; j++) { // Exclude last column (actions)
                var cellText = cols[j].innerText.replace(/"/g, '""');
                row.push('"' + cellText + '"');
            }

            csv.push(row.join(","));
        }

        // Download CSV file
        var csvFile = new Blob([csv.join("\n")], {type: "text/csv"});
        var downloadLink = document.createElement("a");
        downloadLink.download = filename;
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }
});
</script>
<?= $this->endSection() ?>
