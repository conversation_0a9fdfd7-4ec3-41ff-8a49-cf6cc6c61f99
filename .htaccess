# Disable directory browsing
Options -Indexes

# ----------------------------------------------------------------------
# Rewrite engine
# ----------------------------------------------------------------------

# Turning on the rewrite engine is necessary for the following rules and features.
# FollowSymLinks must be enabled for this to work.
<IfModule mod_rewrite.c>
    Options +FollowSymlinks
    RewriteEngine On

    # REMOVE PUBLIC FOLDER FROM URL
    # If the request is not for a valid directory
    RewriteCond %{REQUEST_FILENAME} !-d
    # If the request is not for a valid file
    RewriteCond %{REQUEST_FILENAME} !-f
    # Rewrite all other URLs to public folder
    RewriteRule ^(.*)$ public/$1 [L]

    # Handle requests that go to public folder
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^public/(.*)$ public/index.php/$1 [L,QSA]
</IfModule>

<IfModule !mod_rewrite.c>
    # If we don't have mod_rewrite installed, all 404's
    # can be sent to public/index.php, and everything works as normal.
    ErrorDocument 404 public/index.php
</IfModule>

# Disable server signature
ServerSignature Off
