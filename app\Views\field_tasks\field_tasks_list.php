<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="card">
    <h1 class="card-title">📋 My Tasks</h1>
    <p style="color: #666; margin-bottom: 0;">Your assigned activities and tasks</p>
</div>

<!-- Flash messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>

<!-- Task Statistics -->
<div class="row">
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number"><?= $stats['pending'] ?></span>
            <div class="stat-label">Pending Tasks</div>
        </div>
    </div>
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number"><?= $stats['completed'] ?></span>
            <div class="stat-label">Completed</div>
        </div>
    </div>
</div>

<!-- Task Filters -->
<div class="row">
    <div class="col col-6">
        <button class="btn btn-block" onclick="filterTasks('all')" id="filter-all">
            All Tasks (<?= $stats['total'] ?>)
        </button>
    </div>
    <div class="col col-6">
        <button class="btn btn-secondary btn-block" onclick="filterTasks('pending')" id="filter-pending">
            Pending Only (<?= $stats['pending'] ?>)
        </button>
    </div>
</div>

<!-- Tasks List -->
<div id="tasks-container">
    <?php if (empty($tasks)): ?>
        <div class="card">
            <div style="text-align: center; padding: 20px; color: #666;">
                <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                <h3>No Tasks Assigned</h3>
                <p>You don't have any tasks assigned at the moment.</p>
                <a href="<?= base_url('field/dashboard') ?>" class="btn">← Back to Dashboard</a>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($tasks as $task): ?>
            <?php
            // Determine task status and priority
            $statusClass = '';
            $statusText = '';
            $priorityClass = '';
            $priorityText = '';
            
            switch ($task['status']) {
                case 'active':
                    $statusClass = 'pending';
                    $statusText = 'Active';
                    break;
                case 'submitted':
                    $statusClass = 'completed';
                    $statusText = 'Submitted';
                    break;
                case 'approved':
                    $statusClass = 'completed';
                    $statusText = 'Approved';
                    break;
                case 'redo':
                    $statusClass = 'pending';
                    $statusText = 'Needs Revision';
                    break;
                case 'cancelled':
                    $statusClass = 'cancelled';
                    $statusText = 'Cancelled';
                    break;
                default:
                    $statusClass = 'pending';
                    $statusText = 'Pending';
                    break;
            }
            
            // Determine priority based on date
            $daysUntilDue = (strtotime($task['date_to']) - time()) / (60 * 60 * 24);
            if ($daysUntilDue <= 2) {
                $priorityClass = 'high';
                $priorityText = 'HIGH';
            } elseif ($daysUntilDue <= 7) {
                $priorityClass = 'medium';
                $priorityText = 'MEDIUM';
            } else {
                $priorityClass = 'low';
                $priorityText = 'LOW';
            }
            ?>
            
            <div class="card task-card" data-status="<?= $statusClass ?>" data-priority="<?= $priorityClass ?>">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                    <h3 style="margin: 0; font-size: 16px; color: #333;"><?= esc($task['activity_name']) ?></h3>
                    <span class="priority-badge <?= $priorityClass ?>"><?= $priorityText ?></span>
                </div>
                
                <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
                    📊 Type: <?= esc($task['activity_type']) ?><br>
                    📅 From: <?= date('M j, Y', strtotime($task['date_from'])) ?><br>
                    📅 To: <?= date('M j, Y', strtotime($task['date_to'])) ?><br>
                    🏷️ Status: <span style="color: <?= $statusClass == 'completed' ? '#28a745' : ($statusClass == 'cancelled' ? '#dc3545' : '#ffc107') ?>; font-weight: bold;"><?= $statusText ?></span>
                </div>
                
                <div style="display: flex; gap: 10px;">
                    <a href="<?= base_url('field/tasks/view/' . $task['activity_id']) ?>" class="btn" style="flex: 1; font-size: 14px;">
                        👁️ View Details
                    </a>
                    <?php if ($task['status'] == 'active' || $task['status'] == 'redo'): ?>
                        <button class="btn btn-success" style="flex: 1; font-size: 14px;" onclick="updateTaskStatus(<?= $task['activity_id'] ?>, 'submitted')">
                            ✅ Mark Complete
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Back to Dashboard -->
<div class="card" style="margin-top: 20px;">
    <a href="<?= base_url('field/dashboard') ?>" class="btn btn-secondary btn-block">← Back to Dashboard</a>
</div>

<!-- Status Update Form (Hidden) -->
<form id="statusUpdateForm" method="post" action="<?= base_url('field/tasks/update-status') ?>" style="display: none;">
    <input type="hidden" name="task_id" id="statusTaskId">
    <input type="hidden" name="status" id="statusValue">
    <input type="hidden" name="remarks" id="statusRemarks">
</form>

<style>
/* Task Cards */
.task-card {
    margin-bottom: 15px;
    position: relative;
}

/* Priority Badges */
.priority-badge {
    font-size: 10px;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
}

.priority-badge.high {
    background: #dc3545;
}

.priority-badge.medium {
    background: #ffc107;
    color: #333;
}

.priority-badge.low {
    background: #28a745;
}

/* Task Filtering */
.task-card[data-status="completed"] {
    opacity: 0.8;
}

.task-card[data-status="cancelled"] {
    opacity: 0.6;
}

/* Filter buttons */
.btn.active {
    background: #007bff;
    color: white;
}

/* Stat cards */
.stat-card {
    text-align: center;
    padding: 15px;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}
</style>

<script>
// Task filtering
function filterTasks(filter) {
    const tasks = document.querySelectorAll('.task-card');
    const buttons = document.querySelectorAll('[onclick^="filterTasks"]');
    
    // Update button states
    buttons.forEach(btn => btn.classList.remove('active'));
    document.getElementById('filter-' + filter).classList.add('active');
    
    // Filter tasks
    tasks.forEach(task => {
        if (filter === 'all') {
            task.style.display = 'block';
        } else if (filter === 'pending') {
            const status = task.getAttribute('data-status');
            task.style.display = (status === 'pending') ? 'block' : 'none';
        }
    });
}

// Update task status
function updateTaskStatus(taskId, status) {
    if (confirm('Are you sure you want to mark this task as ' + status + '?')) {
        document.getElementById('statusTaskId').value = taskId;
        document.getElementById('statusValue').value = status;
        document.getElementById('statusUpdateForm').submit();
    }
}

// Initialize with all tasks shown
document.addEventListener('DOMContentLoaded', function() {
    filterTasks('all');
});
</script>

<?= $this->endSection() ?>
