<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-building me-2"></i>Organization Details
                            </h2>
                            <p class="text-light mb-0">View organization information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary me-2">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                            <a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit Organization
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organization Details -->
    <div class="row">
        <div class="col-lg-10 col-md-12 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-building me-2"></i><?= esc($organization['org_name']) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Organization Icon and Basic Info -->
                        <div class="col-md-4 text-center mb-4">
                            <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                                <i class="bi bi-building text-white" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="text-light"><?= esc($organization['org_name']) ?></h4>
                            <p class="text-muted">
                                <code class="text-info"><?= esc($organization['org_code']) ?></code>
                            </p>
                            
                            <div class="mt-3">
                                <?php if ($organization['is_active']): ?>
                                    <span class="badge bg-success fs-6">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary fs-6">Inactive</span>
                                <?php endif; ?>
                                
                                <?php if ($organization['is_locationlocked']): ?>
                                    <br><span class="badge bg-warning text-dark fs-6 mt-2">Location Locked</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Organization Information -->
                        <div class="col-md-8">
                            <h6 class="text-light mb-3">Organization Information</h6>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Organization ID:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="text-muted"><?= esc($organization['id']) ?></span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Organization Code:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <code class="text-info"><?= esc($organization['org_code']) ?></code>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Organization Name:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="text-muted"><?= esc($organization['org_name']) ?></span>
                                </div>
                            </div>

                            <?php if ($organization['description']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong class="text-light">Description:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-muted"><?= esc($organization['description']) ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Location Information -->
                            <?php if ($organization['country_name'] || $organization['province_name'] || $organization['postal_address']): ?>
                                <h6 class="text-light mb-3 mt-4">Location Information</h6>
                                
                                <?php if ($organization['country_name']): ?>
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong class="text-light">Country:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="text-muted"><?= esc($organization['country_name']) ?></span>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($organization['province_name']): ?>
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong class="text-light">Province/State:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="text-muted"><?= esc($organization['province_name']) ?></span>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($organization['postal_address']): ?>
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong class="text-light">Postal Address:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="text-muted"><?= nl2br(esc($organization['postal_address'])) ?></span>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Contact Information -->
                            <?php if ($organization['phone_numbers'] || $organization['email_addresses']): ?>
                                <h6 class="text-light mb-3 mt-4">Contact Information</h6>
                                
                                <?php if ($organization['phone_numbers']): ?>
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong class="text-light">Phone Numbers:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="text-muted">
                                                <i class="bi bi-telephone me-2"></i><?= esc($organization['phone_numbers']) ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if ($organization['email_addresses']): ?>
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <strong class="text-light">Email Address:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="text-muted">
                                                <i class="bi bi-envelope me-2"></i>
                                                <a href="mailto:<?= esc($organization['email_addresses']) ?>" class="text-primary text-decoration-none">
                                                    <?= esc($organization['email_addresses']) ?>
                                                </a>
                                            </span>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Settings -->
                            <h6 class="text-light mb-3 mt-4">Settings & Status</h6>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Organization Status:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <?php if ($organization['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if ($organization['license_status']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong class="text-light">License Status:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <?php
                                        $licenseColors = [
                                            'active' => 'success',
                                            'expired' => 'danger',
                                            'trial' => 'warning',
                                            'suspended' => 'secondary'
                                        ];
                                        $color = $licenseColors[strtolower($organization['license_status'])] ?? 'info';
                                        ?>
                                        <span class="badge bg-<?= $color ?>"><?= ucfirst($organization['license_status']) ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Location Lock:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <?php if ($organization['is_locationlocked']): ?>
                                        <span class="badge bg-warning text-dark">Location Locked</span>
                                    <?php else: ?>
                                        <span class="badge bg-info">Not Locked</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Audit Information -->
                            <h6 class="text-light mb-3 mt-4">Audit Information</h6>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4">
                                    <strong class="text-light">Created:</strong>
                                </div>
                                <div class="col-sm-8">
                                    <span class="text-muted"><?= date('M d, Y H:i', strtotime($organization['created_at'])) ?></span>
                                </div>
                            </div>

                            <?php if ($organization['updated_at']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <strong class="text-light">Last Updated:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-muted"><?= date('M d, Y H:i', strtotime($organization['updated_at'])) ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary">
                                    <i class="bi bi-list me-2"></i>Back to List
                                </a>
                                <a href="<?= base_url('dakoii/organizations/' . $organization['id'] . '/edit') ?>" class="btn btn-warning">
                                    <i class="bi bi-pencil me-2"></i>Edit Organization
                                </a>
                                <form method="post" action="<?= base_url('dakoii/organizations/' . $organization['id'] . '/delete') ?>" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this organization?')">
                                    <?= csrf_field() ?>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="bi bi-trash me-2"></i>Delete Organization
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 120px;
    height: 120px;
}
</style>
<?= $this->endSection() ?>
