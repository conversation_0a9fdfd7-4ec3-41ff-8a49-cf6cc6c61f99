<?php

namespace App\Models;

use CodeIgniter\Model;

class GeoDistrictModel extends Model
{
    protected $table = 'geo_districts';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    
    protected $allowedFields = [
        'district_code',
        'name',
        'country_id',
        'province_id',
        'json_id',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    
    protected $validationRules = [
        'name' => 'required|max_length[100]',
        'country_id' => 'required|integer',
        'province_id' => 'required|integer',
        'district_code' => 'permit_empty|max_length[10]',
        'json_id' => 'permit_empty|max_length[50]',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'name' => [
            'required' => 'District name is required',
            'max_length' => 'District name cannot exceed 100 characters'
        ],
        'country_id' => [
            'required' => 'Country is required',
            'integer' => 'Invalid country selection'
        ],
        'province_id' => [
            'required' => 'Province is required',
            'integer' => 'Invalid province selection'
        ],
        'district_code' => [
            'max_length' => 'District code cannot exceed 10 characters'
        ],
        'json_id' => [
            'max_length' => 'JSON ID cannot exceed 50 characters'
        ],
        'created_by' => [
            'integer' => 'Invalid user ID for created_by'
        ],
        'updated_by' => [
            'integer' => 'Invalid user ID for updated_by'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Get all districts for dropdown
     */
    public function getDistrictsForDropdown()
    {
        $districts = $this->orderBy('name', 'ASC')->findAll();
        $dropdown = [];
        
        foreach ($districts as $district) {
            $dropdown[$district['id']] = $district['name'];
        }
        
        return $dropdown;
    }
    
    /**
     * Get districts by province for dropdown
     */
    public function getDistrictsByProvinceForDropdown(int $provinceId)
    {
        $districts = $this->where('province_id', $provinceId)
                         ->orderBy('name', 'ASC')
                         ->findAll();
        
        $dropdown = [];
        foreach ($districts as $district) {
            $dropdown[$district['id']] = $district['name'];
        }
        
        return $dropdown;
    }
    
    /**
     * Get districts by country for dropdown
     */
    public function getDistrictsByCountryForDropdown(int $countryId)
    {
        $districts = $this->where('country_id', $countryId)
                         ->orderBy('name', 'ASC')
                         ->findAll();
        
        $dropdown = [];
        foreach ($districts as $district) {
            $dropdown[$district['id']] = $district['name'];
        }
        
        return $dropdown;
    }
    
    /**
     * Get districts with province and country information
     */
    public function getDistrictsWithDetails()
    {
        return $this->select('geo_districts.*, geo_provinces.name as province_name, geo_countries.name as country_name')
                   ->join('geo_provinces', 'geo_provinces.id = geo_districts.province_id', 'left')
                   ->join('geo_countries', 'geo_countries.id = geo_districts.country_id', 'left')
                   ->orderBy('geo_countries.name', 'ASC')
                   ->orderBy('geo_provinces.name', 'ASC')
                   ->orderBy('geo_districts.name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get districts by province
     */
    public function getDistrictsByProvince(int $provinceId)
    {
        return $this->select('geo_districts.*, geo_countries.name as country_name')
                   ->join('geo_countries', 'geo_countries.id = geo_districts.country_id', 'left')
                   ->where('geo_districts.province_id', $provinceId)
                   ->orderBy('geo_districts.name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get districts by country
     */
    public function getDistrictsByCountry(int $countryId)
    {
        return $this->select('geo_districts.*, geo_provinces.name as province_name')
                   ->join('geo_provinces', 'geo_provinces.id = geo_districts.province_id', 'left')
                   ->where('geo_districts.country_id', $countryId)
                   ->orderBy('geo_provinces.name', 'ASC')
                   ->orderBy('geo_districts.name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get district by code
     */
    public function getByCode(string $districtCode)
    {
        return $this->where('district_code', $districtCode)->first();
    }
    
    /**
     * Get district by JSON ID
     */
    public function getByJsonId(string $jsonId)
    {
        return $this->where('json_id', $jsonId)->first();
    }
    
    /**
     * Search districts
     */
    public function searchDistricts(string $keyword, int $countryId = null, int $provinceId = null)
    {
        $builder = $this->select('geo_districts.*, geo_provinces.name as province_name, geo_countries.name as country_name')
                       ->join('geo_provinces', 'geo_provinces.id = geo_districts.province_id', 'left')
                       ->join('geo_countries', 'geo_countries.id = geo_districts.country_id', 'left')
                       ->groupStart()
                           ->like('geo_districts.name', $keyword)
                           ->orLike('geo_districts.district_code', $keyword)
                           ->orLike('geo_provinces.name', $keyword)
                           ->orLike('geo_countries.name', $keyword)
                       ->groupEnd();
        
        if ($countryId) {
            $builder->where('geo_districts.country_id', $countryId);
        }
        
        if ($provinceId) {
            $builder->where('geo_districts.province_id', $provinceId);
        }
        
        return $builder->orderBy('geo_countries.name', 'ASC')
                      ->orderBy('geo_provinces.name', 'ASC')
                      ->orderBy('geo_districts.name', 'ASC')
                      ->findAll();
    }
    
    /**
     * Get district statistics
     */
    public function getDistrictStats()
    {
        $stats = [];
        
        // Total districts
        $stats['total'] = $this->countAllResults();
        
        // Districts by country
        $stats['by_country'] = $this->select('geo_countries.name as country_name, COUNT(geo_districts.id) as district_count')
                                   ->join('geo_countries', 'geo_countries.id = geo_districts.country_id', 'left')
                                   ->groupBy('geo_districts.country_id')
                                   ->orderBy('geo_countries.name', 'ASC')
                                   ->findAll();
        
        // Districts by province
        $stats['by_province'] = $this->select('geo_provinces.name as province_name, geo_countries.name as country_name, COUNT(geo_districts.id) as district_count')
                                    ->join('geo_provinces', 'geo_provinces.id = geo_districts.province_id', 'left')
                                    ->join('geo_countries', 'geo_countries.id = geo_districts.country_id', 'left')
                                    ->groupBy('geo_districts.province_id')
                                    ->orderBy('geo_countries.name', 'ASC')
                                    ->orderBy('geo_provinces.name', 'ASC')
                                    ->findAll();
        
        return $stats;
    }
    
    /**
     * Get district with full details
     */
    public function getDistrictWithDetails(int $districtId)
    {
        return $this->select('geo_districts.*, geo_provinces.name as province_name, geo_countries.name as country_name, geo_countries.country_code')
                   ->join('geo_provinces', 'geo_provinces.id = geo_districts.province_id', 'left')
                   ->join('geo_countries', 'geo_countries.id = geo_districts.country_id', 'left')
                   ->where('geo_districts.id', $districtId)
                   ->first();
    }
    
    /**
     * Check if district code is unique
     */
    public function isDistrictCodeUnique(string $districtCode, int $excludeId = null): bool
    {
        $builder = $this->where('district_code', $districtCode);
        
        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() === 0;
    }
    
    /**
     * Get districts with pagination
     */
    public function getDistrictsPaginated(int $perPage = 20, int $page = 1, array $filters = [])
    {
        $builder = $this->select('geo_districts.*, geo_provinces.name as province_name, geo_countries.name as country_name')
                       ->join('geo_provinces', 'geo_provinces.id = geo_districts.province_id', 'left')
                       ->join('geo_countries', 'geo_countries.id = geo_districts.country_id', 'left');
        
        // Apply filters
        if (!empty($filters['country_id'])) {
            $builder->where('geo_districts.country_id', $filters['country_id']);
        }
        
        if (!empty($filters['province_id'])) {
            $builder->where('geo_districts.province_id', $filters['province_id']);
        }
        
        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('geo_districts.name', $filters['search'])
                   ->orLike('geo_districts.district_code', $filters['search'])
                   ->groupEnd();
        }
        
        return $builder->orderBy('geo_countries.name', 'ASC')
                      ->orderBy('geo_provinces.name', 'ASC')
                      ->orderBy('geo_districts.name', 'ASC')
                      ->paginate($perPage, 'default', $page);
    }
}
