<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-person me-2"></i>User Details
                            </h2>
                            <p class="text-muted mb-0">View user information and account details</p>
                        </div>
                        <div>
                            <?= view('partials/back_button', [
                                'href' => base_url('admin/users'),
                                'label' => 'Back to Users List',
                                'class' => 'btn btn-secondary me-2'
                            ]) ?>
                            <a href="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- User Profile Card -->
        <div class="col-lg-4 mb-4">
            <div class="card card-dark">
                <div class="card-body text-center">
                    <!-- Profile Photo -->
                    <div class="mb-3">
                        <?php if ($user['id_photo']): ?>
                            <img src="<?= base_url($user['id_photo']) ?>"
                                 alt="<?= esc($user['name']) ?>"
                                 class="rounded-circle border border-3 border-primary"
                                 style="width: 120px; height: 120px; object-fit: cover;">
                        <?php else: ?>
                            <div class="bg-secondary rounded-circle border border-3 border-primary mx-auto d-flex align-items-center justify-content-center" 
                                 style="width: 120px; height: 120px;">
                                <i class="bi bi-person display-4 text-white"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Basic Info -->
                    <h4 class="text-primary-custom mb-1"><?= esc($user['name']) ?></h4>
                    <?php if ($user['position']): ?>
                        <p class="text-muted mb-2"><?= esc($user['position']) ?></p>
                    <?php endif; ?>
                    
                    <!-- System Number -->
                    <div class="mb-3">
                        <span class="badge bg-info fs-6">System #<?= esc($user['sys_no']) ?></span>
                    </div>

                    <!-- Status -->
                    <div class="mb-3">
                        <?php
                        $statusClass = match($user['status']) {
                            'active' => 'success',
                            'pending' => 'warning',
                            'inactive' => 'danger',
                            default => 'secondary'
                        };
                        ?>
                        <span class="badge bg-<?= $statusClass ?> fs-6">
                            <?= ucfirst(esc($user['status'])) ?>
                        </span>
                    </div>

                    <!-- Permissions -->
                    <div class="d-flex justify-content-center gap-2 mb-3">
                        <?php if ($user['is_admin']): ?>
                            <span class="badge bg-danger">
                                <i class="bi bi-shield-check me-1"></i>Admin
                            </span>
                        <?php endif; ?>
                        <?php if ($user['is_supervisor']): ?>
                            <span class="badge bg-info">
                                <i class="bi bi-person-badge me-1"></i>Supervisor
                            </span>
                        <?php endif; ?>
                        <?php if (!$user['is_admin'] && !$user['is_supervisor']): ?>
                            <span class="badge bg-secondary">Regular User</span>
                        <?php endif; ?>
                    </div>

                    <!-- Contact Actions -->
                    <div class="d-grid gap-2">
                        <a href="mailto:<?= esc($user['email']) ?>" class="btn btn-outline-primary">
                            <i class="bi bi-envelope me-2"></i>Send Email
                        </a>
                        <?php if ($user['phone']): ?>
                            <a href="tel:<?= esc($user['phone']) ?>" class="btn btn-outline-success">
                                <i class="bi bi-telephone me-2"></i>Call
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Information -->
        <div class="col-lg-8">
            <!-- Contact Information -->
            <div class="card card-dark mb-4">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-person-lines-fill me-2"></i>Contact Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Email Address</label>
                            <div class="fw-medium">
                                <a href="mailto:<?= esc($user['email']) ?>" class="text-decoration-none">
                                    <?= esc($user['email']) ?>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Phone Number</label>
                            <div class="fw-medium">
                                <?php if ($user['phone']): ?>
                                    <a href="tel:<?= esc($user['phone']) ?>" class="text-decoration-none">
                                        <?= esc($user['phone']) ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Organization Information -->
            <div class="card card-dark mb-4">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-building me-2"></i>Organization Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($user['org_name']): ?>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Organization Name</label>
                                <div class="fw-medium"><?= esc($user['org_name']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Organization Code</label>
                                <div class="fw-medium">
                                    <span class="badge bg-secondary"><?= esc($user['org_code']) ?></span>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="bi bi-building text-muted display-6"></i>
                            <p class="text-muted mt-2">No organization assigned</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card card-dark mb-4">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-gear me-2"></i>Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Role</label>
                            <div class="fw-medium">
                                <span class="badge bg-<?= $user['role'] === 'user' ? 'primary' : 'secondary' ?>">
                                    <?= ucfirst(esc($user['role'])) ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Reports To</label>
                            <div class="fw-medium">
                                <?php if (!empty($user['supervisor_name'])): ?>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-person-up text-warning me-2"></i>
                                        <div>
                                            <div><?= esc($user['supervisor_name']) ?></div>
                                            <?php if (!empty($user['supervisor_email'])): ?>
                                                <small class="text-muted"><?= esc($user['supervisor_email']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">No supervisor assigned</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Account Created</label>
                            <div class="fw-medium">
                                <?= date('F j, Y \a\t g:i A', strtotime($user['created_at'])) ?>
                            </div>
                        </div>
                        <?php if ($user['activated_at']): ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">Account Activated</label>
                                <div class="fw-medium">
                                    <?= date('F j, Y \a\t g:i A', strtotime($user['activated_at'])) ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Last Updated</label>
                            <div class="fw-medium">
                                <?= date('F j, Y \a\t g:i A', strtotime($user['updated_at'])) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="text-primary-custom mb-0">
                            <i class="bi bi-tools me-2"></i>Quick Actions
                        </h6>
                        <div class="btn-group" role="group">
                            <a href="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>"
                               class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
