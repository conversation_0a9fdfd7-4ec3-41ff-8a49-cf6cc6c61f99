<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-building me-2"></i><?= esc($entity['business_name']) ?>
            </h1>
            <p class="text-muted mb-0">Business entity details and information</p>
        </div>
        <div>
            <a href="<?= base_url('admin/business-entities') ?>" class="btn btn-secondary me-2">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
            <a href="<?= base_url('admin/business-entities/' . $entity['id'] . '/edit') ?>" class="btn btn-primary">
                <i class="bi bi-pencil me-1"></i>Edit
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Business Entity Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle me-2"></i>Business Entity Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Status</label>
                            <div class="form-control-plaintext">
                                <?php
                                $statusClass = match($entity['status']) {
                                    'active' => 'success',
                                    'inactive' => 'danger',
                                    default => 'secondary'
                                };
                                ?>
                                <span class="badge bg-<?= $statusClass ?>">
                                    <?= ucfirst(esc($entity['status'])) ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label text-muted">Business Name</label>
                            <div class="form-control-plaintext">
                                <strong><?= esc($entity['business_name']) ?></strong>
                            </div>
                        </div>
                    </div>

                    <?php if ($entity['remarks']): ?>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label text-muted">Remarks</label>
                            <div class="form-control-plaintext">
                                <?= nl2br(esc($entity['remarks'])) ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Total Locations</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary">
                                    <?= $entity['locations_count'] ?> locations
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Information -->
        <div class="col-lg-4">
            <!-- Status Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-clock-history me-2"></i>Status History
                    </h6>
                </div>
                <div class="card-body">
                    <?php if ($entity['status_at']): ?>
                    <div class="mb-3">
                        <small class="text-muted">Status Changed</small>
                        <div><?= date('M d, Y H:i', strtotime($entity['status_at'])) ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($entity['status_remarks']): ?>
                    <div class="mb-3">
                        <small class="text-muted">Status Remarks</small>
                        <div><?= nl2br(esc($entity['status_remarks'])) ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Audit Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-file-text me-2"></i>Record Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <div><?= date('M d, Y H:i', strtotime($entity['created_at'])) ?></div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">Last Updated</small>
                        <div><?= date('M d, Y H:i', strtotime($entity['updated_at'])) ?></div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-lightning me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('admin/business-locations?entity=' . $entity['id']) ?>" 
                           class="btn btn-outline-primary">
                            <i class="bi bi-geo-alt me-1"></i>View Locations
                        </a>
                        <a href="<?= base_url('admin/business-locations/new?entity=' . $entity['id']) ?>" 
                           class="btn btn-outline-success">
                            <i class="bi bi-plus-circle me-1"></i>Add Location
                        </a>
                        <hr>
                        <a href="<?= base_url('admin/business-entities/' . $entity['id'] . '/edit') ?>" 
                           class="btn btn-outline-warning">
                            <i class="bi bi-pencil me-1"></i>Edit Entity
                        </a>
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="confirmDelete(<?= $entity['id'] ?>, '<?= esc($entity['business_name']) ?>')">
                            <i class="bi bi-trash me-1"></i>Delete Entity
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the business entity "<span id="entityName"></span>"?</p>
                <p class="text-danger small">This action cannot be undone and will affect all related locations.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, name) {
    document.getElementById('entityName').textContent = name;
    document.getElementById('deleteForm').action = '<?= base_url('admin/business-entities') ?>/' + id;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>
