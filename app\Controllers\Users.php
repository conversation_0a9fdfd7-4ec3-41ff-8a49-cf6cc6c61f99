<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\DakoiiOrgModel;
use CodeIgniter\Controller;

class Users extends Controller
{
    protected $userModel;
    protected $dakoiiOrgModel;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        helper('dakoii');
    }
    
    /**
     * Check if user is logged in
     */
    private function checkAuth()
    {
        if (!session()->get('dakoii_logged_in')) {
            return redirect()->to('dakoii')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of users
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $users = $this->userModel->getUsersWithOrg();
        
        $data = [
            'title' => 'Users Management',
            'users' => $users
        ];
        
        return view('users/users_index', $data);
    }
    
    /**
     * Display form to create new user
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $organizations = $this->dakoiiOrgModel->getActiveOrgs();

        // Get supervisors for reports_to dropdown
        $supervisors = $this->userModel->getSupervisorUsers();

        $data = [
            'title' => 'Add New User',
            'organizations' => $organizations,
            'supervisors' => $supervisors
        ];

        return view('users/users_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'org_id' => 'required|integer',
            'name' => 'required|max_length[255]',
            'email' => 'required|valid_email|max_length[500]|is_unique[users.email]',
            'phone' => 'permit_empty|max_length[200]',
            'role' => 'required|in_list[user,guest]',
            'is_admin' => 'permit_empty|in_list[0,1]',
            'is_supervisor' => 'permit_empty|in_list[0,1]',
            'position' => 'permit_empty|max_length[255]'
        ];

        // Add file validation only if a file is uploaded
        $idPhotoFile = $this->request->getFile('id_photo');
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            $rules['id_photo'] = 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png,gif]';
        }
        
        if (!$this->validate($rules)) {
            $organizations = $this->dakoiiOrgModel->getActiveOrgs();
            
            $data = [
                'title' => 'Add New User',
                'organizations' => $organizations,
                'validation' => $this->validator
            ];
            
            return view('users/users_create', $data);
        }
        
        // Handle file upload for ID photo
        $idPhotoPath = null;
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            // Ensure upload directory exists
            $uploadPath = FCPATH . 'uploads/id_photos/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $newName = $idPhotoFile->getRandomName();
            $idPhotoFile->move($uploadPath, $newName);
            $idPhotoPath = 'public/uploads/id_photos/' . $newName; // Add public/ prefix for web access
        }

        $userData = [
            'org_id' => $this->request->getPost('org_id'),
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'password' => 'temp_' . bin2hex(random_bytes(8)), // Temporary password, will be replaced on activation
            'phone' => $this->request->getPost('phone'),
            'role' => $this->request->getPost('role'),
            'is_admin' => $this->request->getPost('is_admin') ? 1 : 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'reports_to' => $this->request->getPost('reports_to') ?: null,
            'position' => $this->request->getPost('position'),
            'id_photo' => $idPhotoPath,
            'status' => 'pending', // Set as pending initially for activation
            'created_by' => session()->get('dakoii_user_id')
        ];

        $userId = $this->userModel->insert($userData);

        if ($userId) {
            // Generate activation token and send email
            $token = $this->userModel->generateActivationToken($userId);

            if ($this->sendActivationEmail($userData['email'], $userData['name'], $token)) {
                return redirect()->to('dakoii/system-users')->with('success', 'User created successfully. Activation email sent to ' . $userData['email']);
            } else {
                return redirect()->to('dakoii/system-users')->with('warning', 'User created but failed to send activation email. Please resend manually.');
            }
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create user.');
        }
    }
    
    /**
     * Display user details
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->select('users.*, dakoii_org.org_name, dakoii_org.org_code')
                               ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                               ->where('users.id', $id)
                               ->where('users.deleted_at', null)
                               ->first();
        
        if (!$user) {
            return redirect()->to('dakoii/system-users')->with('error', 'User not found.');
        }
        
        $data = [
            'title' => 'View User',
            'user' => $user
        ];
        
        return view('users/users_show', $data);
    }
    
    /**
     * Display form to edit user
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $user = $this->userModel->find($id);

        if (!$user) {
            return redirect()->to('dakoii/system-users')->with('error', 'User not found.');
        }

        $organizations = $this->dakoiiOrgModel->getActiveOrgs();

        // Get supervisors for reports_to dropdown
        $supervisors = $this->userModel->getSupervisorUsers();

        $data = [
            'title' => 'Edit User',
            'user' => $user,
            'organizations' => $organizations,
            'supervisors' => $supervisors
        ];

        return view('users/users_edit', $data);
    }
    
    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/system-users')->with('error', 'User not found.');
        }

        $rules = [
            'org_id' => 'required|integer',
            'name' => 'required|max_length[255]',
            'email' => "required|valid_email|max_length[500]|is_unique[users.email,id,{$id}]",
            'phone' => 'permit_empty|max_length[200]',
            'role' => 'required|in_list[user,guest]',
            'is_admin' => 'permit_empty|in_list[0,1]',
            'is_supervisor' => 'permit_empty|in_list[0,1]',
            'position' => 'permit_empty|max_length[255]',
            'status' => 'required|in_list[active,inactive,suspended,pending]'
        ];

        // Add password validation only if password is provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[4]';
            $rules['confirm_password'] = 'matches[password]';
        }

        // Add file validation only if a file is uploaded
        $idPhotoFile = $this->request->getFile('id_photo');
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            $rules['id_photo'] = 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png,gif]';
        }
        
        if (!$this->validate($rules)) {
            $organizations = $this->dakoiiOrgModel->getActiveOrgs();
            
            $data = [
                'title' => 'Edit User',
                'user' => $user,
                'organizations' => $organizations,
                'validation' => $this->validator
            ];
            
            return view('users/users_edit', $data);
        }
        
        // Handle file upload for ID photo
        $idPhotoPath = $user['id_photo']; // Keep existing photo by default
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            // Ensure upload directory exists
            $uploadPath = FCPATH . 'uploads/id_photos/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Delete old photo if it exists (remove public/ prefix for file system access)
            if (!empty($user['id_photo']) && file_exists(FCPATH . str_replace('public/', '', $user['id_photo']))) {
                unlink(FCPATH . str_replace('public/', '', $user['id_photo']));
            }

            $newName = $idPhotoFile->getRandomName();
            $idPhotoFile->move($uploadPath, $newName);
            $idPhotoPath = 'public/uploads/id_photos/' . $newName; // Add public/ prefix for web access
        }

        $userData = [
            'org_id' => $this->request->getPost('org_id'),
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'role' => $this->request->getPost('role'),
            'is_admin' => $this->request->getPost('is_admin') ? 1 : 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? 1 : 0,
            'reports_to' => $this->request->getPost('reports_to') ?: null,
            'position' => $this->request->getPost('position'),
            'id_photo' => $idPhotoPath,
            'status' => $this->request->getPost('status'),
            'updated_by' => session()->get('dakoii_user_id')
        ];
        
        // Only update password if provided
        if ($this->request->getPost('password')) {
            $userData['password'] = $this->request->getPost('password');
        }
        
        // Temporarily disable model validation to use controller validation only
        $result = $this->userModel->skipValidation()->update($id, $userData);

        if ($result) {
            return redirect()->to('dakoii/system-users')->with('success', 'User updated successfully.');
        } else {
            // Get detailed error information
            $errors = $this->userModel->errors();
            $errorMessage = 'Failed to update user.';
            if (!empty($errors)) {
                $errorMessage .= ' Errors: ' . implode(', ', $errors);
            }

            // Log the error for debugging
            log_message('error', 'User update failed for ID ' . $id . ': ' . print_r($userData, true));
            log_message('error', 'Model errors: ' . print_r($errors, true));

            return redirect()->back()->withInput()->with('error', $errorMessage);
        }
    }
    
    /**
     * Handle delete request
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/system-users')->with('error', 'User not found.');
        }

        // Soft delete with deleted_by tracking
        $deleteData = [
            'deleted_by' => session()->get('dakoii_user_id')
        ];

        if ($this->userModel->update($id, $deleteData) && $this->userModel->delete($id)) {
            return redirect()->to('dakoii/system-users')->with('success', 'User deleted successfully.');
        } else {
            return redirect()->to('dakoii/system-users')->with('error', 'Failed to delete user.');
        }
    }

    /**
     * Send activation email to user
     */
    private function sendActivationEmail(string $email, string $name, string $token): bool
    {
        $emailService = \Config\Services::email();

        $activationLink = base_url("dakoii/activate-account/{$token}");

        $subject = 'Welcome to PCOLLX - Price Collection System';
        $message = "
        <html>
        <head>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 0;
                    background-color: #f4f4f4;
                }
                .email-container {
                    max-width: 650px;
                    margin: 20px auto;
                    background-color: #ffffff;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px 20px;
                    text-align: center;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 300;
                }
                .header p {
                    margin: 5px 0 0 0;
                    font-size: 14px;
                    opacity: 0.9;
                }
                .content {
                    padding: 40px 30px;
                    background-color: #ffffff;
                }
                .welcome-text {
                    font-size: 18px;
                    color: #2c3e50;
                    margin-bottom: 20px;
                }
                .activation-section {
                    background-color: #f8f9fa;
                    border-left: 4px solid #28a745;
                    padding: 20px;
                    margin: 25px 0;
                    border-radius: 0 5px 5px 0;
                }
                .button {
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    margin: 20px 0;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .link-fallback {
                    background-color: #e9ecef;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                    word-break: break-all;
                }
                .footer {
                    background-color: #2c3e50;
                    color: #ecf0f1;
                    text-align: center;
                    padding: 25px 20px;
                }
                .footer p {
                    margin: 5px 0;
                    font-size: 13px;
                }
                .powered-by {
                    font-size: 12px;
                    opacity: 0.8;
                    margin-top: 10px;
                }
                .warning {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                    padding: 12px;
                    border-radius: 5px;
                    margin: 20px 0;
                    font-size: 14px;
                }
            </style>
        </head>
        <body>
            <div class='email-container'>
                <div class='header'>
                    <h1>PCOLLX</h1>
                    <p>Price Collection System</p>
                </div>
                <div class='content'>
                    <div class='welcome-text'>
                        <strong>Welcome to PCOLLX - Price Collection System</strong>
                    </div>
                    <p>Hello <strong>{$name}</strong>,</p>
                    <p>Your account has been successfully created in the PCOLLX system. To complete your registration and start using the platform, please activate your account by clicking the button below:</p>

                    <div class='activation-section'>
                        <p style='text-align: center; margin: 0;'>
                            <a href='{$activationLink}' class='button'>Activate My Account</a>
                        </p>
                    </div>

                    <p><strong>If the button above doesn't work</strong>, please copy and paste the following link into your web browser:</p>
                    <div class='link-fallback'>
                        <a href='{$activationLink}' style='color: #007bff;'>{$activationLink}</a>
                    </div>

                    <div class='warning'>
                        <strong>⚠️ Important:</strong> This activation link will expire in 24 hours for security reasons.
                    </div>

                    <p>Once activated, you will receive a temporary password via email to access your account.</p>
                    <p>If you have any questions or need assistance, please contact your system administrator.</p>
                </div>
                <div class='footer'>
                    <p><strong>PCOLLX - Price Collection System</strong></p>
                    <p class='powered-by'>Developed and Powered by <strong>Dakoii Systems</strong></p>
                    <p>© 2025 Dakoii Systems. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        $emailService->setTo($email);
        $emailService->setSubject($subject);
        $emailService->setMessage($message);

        return $emailService->send();
    }

    /**
     * Handle account activation
     */
    public function activateAccount(string $token)
    {
        $user = $this->userModel->getUserByActivationToken($token);

        if (!$user) {
            return view('users/activation_result', [
                'success' => false,
                'message' => 'Invalid or expired activation token.'
            ]);
        }

        if ($this->userModel->activateAccount($token)) {
            // Generate temporary password
            $tempPassword = $this->userModel->generateTemporaryPassword($user['id']);

            // Send temporary password email
            if ($this->sendTemporaryPasswordEmail($user['email'], $user['name'], $tempPassword)) {
                return view('users/activation_result', [
                    'success' => true,
                    'message' => 'Account activated successfully! A temporary password has been sent to your email.'
                ]);
            } else {
                return view('users/activation_result', [
                    'success' => true,
                    'message' => 'Account activated successfully! However, failed to send temporary password. Please contact administrator.'
                ]);
            }
        } else {
            return view('users/activation_result', [
                'success' => false,
                'message' => 'Failed to activate account. Token may be expired or invalid.'
            ]);
        }
    }

    /**
     * Send temporary password email
     */
    private function sendTemporaryPasswordEmail(string $email, string $name, string $tempPassword): bool
    {
        $emailService = \Config\Services::email();

        $subject = 'PCOLLX Account Activated - Your Login Credentials';
        $message = "
        <html>
        <head>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 0;
                    background-color: #f4f4f4;
                }
                .email-container {
                    max-width: 650px;
                    margin: 20px auto;
                    background-color: #ffffff;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }
                .header {
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    padding: 30px 20px;
                    text-align: center;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 300;
                }
                .header p {
                    margin: 5px 0 0 0;
                    font-size: 14px;
                    opacity: 0.9;
                }
                .content {
                    padding: 40px 30px;
                    background-color: #ffffff;
                }
                .success-text {
                    font-size: 18px;
                    color: #28a745;
                    margin-bottom: 20px;
                    text-align: center;
                }
                .credentials-section {
                    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
                    border: 2px solid #28a745;
                    padding: 25px;
                    margin: 25px 0;
                    border-radius: 10px;
                    text-align: center;
                }
                .password-display {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    background-color: #ffffff;
                    padding: 15px;
                    border-radius: 8px;
                    border: 2px dashed #28a745;
                    margin: 15px 0;
                    letter-spacing: 3px;
                }
                .login-section {
                    background-color: #f8f9fa;
                    border-left: 4px solid #007bff;
                    padding: 20px;
                    margin: 25px 0;
                    border-radius: 0 5px 5px 0;
                }
                .login-button {
                    display: inline-block;
                    padding: 15px 30px;
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    margin: 15px 0;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .security-warning {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                    font-size: 14px;
                }
                .footer {
                    background-color: #2c3e50;
                    color: #ecf0f1;
                    text-align: center;
                    padding: 25px 20px;
                }
                .footer p {
                    margin: 5px 0;
                    font-size: 13px;
                }
                .powered-by {
                    font-size: 12px;
                    opacity: 0.8;
                    margin-top: 10px;
                }
            </style>
        </head>
        <body>
            <div class='email-container'>
                <div class='header'>
                    <h1>✅ Account Activated!</h1>
                    <p>PCOLLX - Price Collection System</p>
                </div>
                <div class='content'>
                    <div class='success-text'>
                        <strong>Welcome to PCOLLX - Price Collection System</strong>
                    </div>
                    <p>Hello <strong>{$name}</strong>,</p>
                    <p>Congratulations! Your PCOLLX account has been successfully activated. You can now access the system using your temporary login credentials below:</p>

                    <div class='credentials-section'>
                        <h3 style='margin-top: 0; color: #28a745;'>🔑 Your Login Credentials</h3>
                        <p><strong>Email:</strong> {$email}</p>
                        <p><strong>Temporary Password:</strong></p>
                        <div class='password-display'>{$tempPassword}</div>
                    </div>

                    <div class='login-section'>
                        <p style='margin-top: 0;'><strong>Ready to get started?</strong></p>
                        <p style='text-align: center; margin-bottom: 0;'>
                            <a href='" . base_url('admin') . "' class='login-button'>Login to PCOLLX</a>
                        </p>
                    </div>

                    <div class='security-warning'>
                        <strong>🔒 Important Security Notice:</strong><br>
                        • This is a temporary password for your first login<br>
                        • Please change your password immediately after logging in<br>
                        • Keep your login credentials secure and confidential
                    </div>

                    <p>If you experience any issues logging in or need assistance, please contact your system administrator.</p>
                </div>
                <div class='footer'>
                    <p><strong>PCOLLX - Price Collection System</strong></p>
                    <p class='powered-by'>Developed and Powered by <strong>Dakoii Systems</strong></p>
                    <p>© 2025 Dakoii Systems. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        $emailService->setTo($email);
        $emailService->setSubject($subject);
        $emailService->setMessage($message);

        return $emailService->send();
    }

    /**
     * AJAX endpoint to check email availability
     */
    public function checkEmail()
    {
        // Only allow AJAX requests
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request']);
        }

        $json = $this->request->getJSON();
        $email = $json->email ?? '';

        if (empty($email)) {
            return $this->response->setJSON(['available' => false, 'message' => 'Email is required']);
        }

        // Check if email exists in database
        $existingUser = $this->userModel->where('email', $email)->first();

        return $this->response->setJSON([
            'available' => $existingUser === null,
            'message' => $existingUser ? 'Email is already taken' : 'Email is available'
        ]);
    }
}
