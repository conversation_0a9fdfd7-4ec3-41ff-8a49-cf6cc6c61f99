<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-plus-circle me-2"></i><?= $title ?>
            </h1>
            <p class="text-muted mb-0">Add a new business entity to the system</p>
        </div>
        <div>
            <a href="<?= base_url('admin/business-entities') ?>" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Error Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-building me-2"></i>Business Entity Information
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= base_url('admin/business-entities/create') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <!-- Business Name -->
                            <div class="col-md-12 mb-3">
                                <label for="business_name" class="form-label">Business Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="business_name" name="business_name" 
                                       value="<?= old('business_name') ?>" required maxlength="150"
                                       placeholder="Enter business entity name">
                                <div class="form-text">Unique name for the business entity (max 150 characters)</div>
                            </div>
                        </div>



                        <div class="row">
                            <!-- Remarks -->
                            <div class="col-md-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                                          placeholder="Optional notes or description about this business entity"><?= old('remarks') ?></textarea>
                                <div class="form-text">Additional information or notes (optional)</div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/business-entities') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-1"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-1"></i>Create Business Entity
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const businessNameInput = document.getElementById('business_name');


    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate business name
        if (!businessNameInput.value.trim()) {
            isValid = false;
            businessNameInput.classList.add('is-invalid');
        } else {
            businessNameInput.classList.remove('is-invalid');
        }
        

        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
    
    // Remove validation classes on input
    businessNameInput.addEventListener('input', function() {
        this.classList.remove('is-invalid');
    });
    

});
</script>

<?= $this->endSection() ?>
