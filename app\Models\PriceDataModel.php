<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * PriceDataModel
 *
 * Model for the `price_data` table.
 * - Uses timestamps and soft deletes
 * - Protects fields via allowedFields
 * - Provides validation matching the schema
 */
class PriceDataModel extends Model
{
    /**
     * Table & Primary Key
     */
    protected $table            = 'price_data';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;

    /**
     * Return type: array for easy JSON/array handling
     */
    protected $returnType       = 'array';

    /**
     * Soft Deletes & Timestamps
     */
    protected $useSoftDeletes   = true;
    protected $useTimestamps    = true;
    protected $dateFormat       = 'datetime'; // created_at/updated_at/deleted_at are DATETIME

    protected $createdField     = 'created_at';
    protected $updatedField     = 'updated_at';
    protected $deletedField     = 'deleted_at';

    /**
     * Mass Assignment Protection
     */
    protected $protectFields    = true;
    protected $allowedFields    = [
        'org_id',
        'user_id',
        'activity_id',
        'business_location_id',
        'item_id',
        'price_amount',
        'effective_date',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Validation Rules
     * Keep simple and aligned with DB constraints.
     */
    protected $validationRules = [
        'org_id'               => 'required|is_natural_no_zero',
        'user_id'              => 'required|is_natural_no_zero',
        'activity_id'          => 'required|is_natural_no_zero',
        'business_location_id' => 'required|is_natural_no_zero',
        'item_id'              => 'required|is_natural_no_zero',
        'price_amount'         => 'required|decimal|greater_than_equal_to[0]',
        // DATE column stored as Y-m-d
        'effective_date'       => 'required|valid_date[Y-m-d]',
        // tinyint(1) flag
        'is_active'            => 'in_list[0,1]',
        // audit users, optional
        'created_by'           => 'permit_empty|is_natural_no_zero',
        'updated_by'           => 'permit_empty|is_natural_no_zero',
        'deleted_by'           => 'permit_empty|is_natural_no_zero',
    ];

    protected $validationMessages = [];
    protected $skipValidation     = false;

    /**
     * Get the latest active price effective on or before the given date.
     *
     * @param int         $itemId
     * @param string      $date   Y-m-d
     * @param int|null    $orgId
     * @param int|null    $businessLocationId
     * @return array|null Returns row as array or null if none found
     */
    public function getActivePriceOnDate(int $itemId, string $date, ?int $orgId = null, ?int $businessLocationId = null): ?array
    {
        $builder = $this->where('item_id', $itemId)
                        ->where('is_active', 1)
                        ->where('effective_date <=', $date);

        if ($orgId !== null) {
            $builder = $builder->where('org_id', $orgId);
        }
        if ($businessLocationId !== null) {
            $builder = $builder->where('business_location_id', $businessLocationId);
        }

        // Latest effective date first, then highest id as tie-breaker
        $row = $builder->orderBy('effective_date', 'DESC')
                       ->orderBy('id', 'DESC')
                       ->first();

        return $row ?: null;
    }

    /**
     * Deactivate currently active prices for the scope by setting is_active = 0.
     * Uses Model's update() flow so timestamps are updated.
     *
     * @param int      $itemId
     * @param int|null $orgId
     * @param int|null $businessLocationId
     * @return int Affected rows
     */
    public function deactivateCurrentPrices(int $itemId, ?int $orgId = null, ?int $businessLocationId = null): int
    {
        $query = $this->where('item_id', $itemId)
                      ->where('is_active', 1);

        if ($orgId !== null) {
            $query = $query->where('org_id', $orgId);
        }
        if ($businessLocationId !== null) {
            $query = $query->where('business_location_id', $businessLocationId);
        }

        $query->set('is_active', 0)->update();

        return $this->db->affectedRows();
    }
}
