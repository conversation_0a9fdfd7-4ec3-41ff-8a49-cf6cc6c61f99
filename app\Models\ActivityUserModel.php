<?php

namespace App\Models;

use CodeIgniter\Model;

class ActivityUserModel extends Model
{
    protected $table = 'activity_users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    
    protected $allowedFields = [
        'org_id',
        'activity_id',
        'user_id',
        'assigned_at'
    ];
    
    protected $useTimestamps = false;
    
    protected $validationRules = [
        'org_id' => 'required|integer',
        'activity_id' => 'required|integer',
        'user_id' => 'required|integer',
        'assigned_at' => 'permit_empty|valid_date'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization is required',
            'integer' => 'Invalid organization selection'
        ],
        'activity_id' => [
            'required' => 'Activity is required',
            'integer' => 'Invalid activity selection'
        ],
        'user_id' => [
            'required' => 'User is required',
            'integer' => 'Invalid user selection'
        ],
        'assigned_at' => [
            'valid_date' => 'Invalid assignment date format'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Custom validation to check unique assignment
     */
    protected $beforeInsert = ['validateUniqueAssignment'];
    
    protected function validateUniqueAssignment(array $data)
    {
        if (isset($data['data']['activity_id']) && isset($data['data']['user_id'])) {
            $existing = $this->where('activity_id', $data['data']['activity_id'])
                           ->where('user_id', $data['data']['user_id'])
                           ->first();
            
            if ($existing) {
                throw new \CodeIgniter\Database\Exceptions\DatabaseException('User is already assigned to this activity');
            }
        }
        
        return $data;
    }
    
    /**
     * Get users assigned to an activity
     */
    public function getUsersByActivity(int $activityId)
    {
        return $this->select('activity_users.*, users.name, users.email, users.phone, users.position')
                   ->join('users', 'users.id = activity_users.user_id', 'left')
                   ->where('activity_users.activity_id', $activityId)
                   ->orderBy('activity_users.assigned_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get activities assigned to a user
     */
    public function getActivitiesByUser(int $userId)
    {
        return $this->select('activity_users.*, activities.activity_name, activities.activity_type, activities.date_from, activities.date_to, activities.status')
                   ->join('activities', 'activities.id = activity_users.activity_id', 'left')
                   ->where('activity_users.user_id', $userId)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activities.date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get activities assigned to a user by organization
     */
    public function getActivitiesByUserAndOrg(int $userId, int $orgId)
    {
        return $this->select('activity_users.*, activities.activity_name, activities.activity_type, activities.date_from, activities.date_to, activities.status')
                   ->join('activities', 'activities.id = activity_users.activity_id', 'left')
                   ->where('activity_users.user_id', $userId)
                   ->where('activity_users.org_id', $orgId)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activities.date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Assign user to activity
     */
    public function assignUserToActivity(int $orgId, int $activityId, int $userId)
    {
        $data = [
            'org_id' => $orgId,
            'activity_id' => $activityId,
            'user_id' => $userId,
            'assigned_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->insert($data);
    }
    
    /**
     * Assign multiple users to activity
     */
    public function assignUsersToActivity(int $orgId, int $activityId, array $userIds)
    {
        $data = [];
        $assignedAt = date('Y-m-d H:i:s');
        
        foreach ($userIds as $userId) {
            $data[] = [
                'org_id' => $orgId,
                'activity_id' => $activityId,
                'user_id' => $userId,
                'assigned_at' => $assignedAt
            ];
        }
        
        return $this->insertBatch($data);
    }
    
    /**
     * Remove user from activity
     */
    public function removeUserFromActivity(int $activityId, int $userId)
    {
        return $this->where('activity_id', $activityId)
                   ->where('user_id', $userId)
                   ->delete();
    }
    
    /**
     * Remove all users from activity
     */
    public function removeAllUsersFromActivity(int $activityId)
    {
        return $this->where('activity_id', $activityId)
                   ->delete();
    }
    
    /**
     * Check if user is assigned to activity
     */
    public function isUserAssignedToActivity(int $activityId, int $userId)
    {
        $assignment = $this->where('activity_id', $activityId)
                          ->where('user_id', $userId)
                          ->first();
        
        return !empty($assignment);
    }
    
    /**
     * Get assignment statistics by activity
     */
    public function getAssignmentStatsByActivity(int $activityId)
    {
        return $this->where('activity_id', $activityId)
                   ->countAllResults();
    }
    
    /**
     * Get assignment statistics by user
     */
    public function getAssignmentStatsByUser(int $userId, int $orgId = null)
    {
        $builder = $this->where('user_id', $userId);
        
        if ($orgId) {
            $builder->where('org_id', $orgId);
        }
        
        return $builder->countAllResults();
    }
    
    /**
     * Get users assigned to activities in a workplan
     */
    public function getUsersByWorkplan(int $workplanId)
    {
        return $this->select('activity_users.*, users.username, users.email, users.first_name, users.last_name, activities.activity_name')
                   ->join('activities', 'activities.id = activity_users.activity_id', 'left')
                   ->join('users', 'users.id = activity_users.user_id', 'left')
                   ->where('activities.workplan_id', $workplanId)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activities.activity_name', 'ASC')
                   ->orderBy('users.first_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get activity assignments by organization
     */
    public function getAssignmentsByOrg(int $orgId)
    {
        return $this->select('activity_users.*, activities.activity_name, activities.activity_type, users.username, users.first_name, users.last_name')
                   ->join('activities', 'activities.id = activity_users.activity_id', 'left')
                   ->join('users', 'users.id = activity_users.user_id', 'left')
                   ->where('activity_users.org_id', $orgId)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activity_users.assigned_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get user workload (count of active assignments)
     */
    public function getUserWorkload(int $userId, int $orgId = null)
    {
        $builder = $this->select('activity_users.*')
                       ->join('activities', 'activities.id = activity_users.activity_id', 'left')
                       ->where('activity_users.user_id', $userId)
                       ->where('activities.status', 'active')
                       ->where('activities.is_deleted', false);
        
        if ($orgId) {
            $builder->where('activity_users.org_id', $orgId);
        }
        
        return $builder->countAllResults();
    }
}
