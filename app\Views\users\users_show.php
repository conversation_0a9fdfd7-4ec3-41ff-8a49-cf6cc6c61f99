<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-person me-2"></i>View User
                            </h2>
                            <p class="text-light mb-0">Detailed information about <?= esc($user['name']) ?></p>
                        </div>
                        <div>
                            <?= view('partials/back_button', [
                                'href' => base_url('dakoii/system-users'),
                                'label' => 'Back to Users List',
                                'class' => 'btn btn-secondary'
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details -->
    <div class="row">
        <!-- Basic Information -->
        <div class="col-md-8">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-person-fill me-2"></i>Basic Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">System Number</label>
                            <div class="fw-bold">
                                <span class="badge bg-info fs-6"><?= esc($user['sys_no']) ?></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Full Name</label>
                            <div class="fw-bold"><?= esc($user['name']) ?></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Email Address</label>
                            <div class="fw-bold">
                                <a href="mailto:<?= esc($user['email']) ?>" class="text-info">
                                    <?= esc($user['email']) ?>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Phone Number</label>
                            <div class="fw-bold">
                                <?= $user['phone'] ? esc($user['phone']) : '<span class="text-muted">Not provided</span>' ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Position</label>
                            <div class="fw-bold">
                                <?= $user['position'] ? esc($user['position']) : '<span class="text-muted">Not specified</span>' ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">ID Photo</label>
                            <div class="fw-bold">
                                <?php if ($user['id_photo']): ?>
                                    <div class="mb-2">
                                        <img src="<?= base_url($user['id_photo']) ?>" alt="ID Photo"
                                             class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                    </div>
                                    <small class="text-muted"><?= esc(basename($user['id_photo'])) ?></small>
                                <?php else: ?>
                                    <span class="text-muted">Not provided</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status & Permissions -->
        <div class="col-md-4">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-shield-check me-2"></i>Status & Permissions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Role</label>
                        <div>
                            <?php
                            $roleClass = $user['role'] === 'user' ? 'bg-success' : 'bg-warning';
                            ?>
                            <span class="badge <?= $roleClass ?> fs-6">
                                <?= ucfirst($user['role']) ?>
                            </span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Status</label>
                        <div>
                            <?php
                            $statusClass = match($user['status']) {
                                'active' => 'bg-success',
                                'inactive' => 'bg-secondary',
                                'suspended' => 'bg-danger',
                                default => 'bg-info'
                            };
                            ?>
                            <span class="badge <?= $statusClass ?> fs-6">
                                <?= ucfirst($user['status']) ?>
                            </span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Administrator</label>
                        <div>
                            <?php if ($user['is_admin']): ?>
                                <i class="bi bi-check-circle-fill text-success me-2"></i>
                                <span class="text-success">Yes</span>
                            <?php else: ?>
                                <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                <span class="text-danger">No</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Supervisor</label>
                        <div>
                            <?php if ($user['is_supervisor']): ?>
                                <i class="bi bi-check-circle-fill text-success me-2"></i>
                                <span class="text-success">Yes</span>
                            <?php else: ?>
                                <i class="bi bi-x-circle-fill text-danger me-2"></i>
                                <span class="text-danger">No</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organization Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-building me-2"></i>Organization Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Organization Name</label>
                            <div class="fw-bold">
                                <?= $user['org_name'] ? esc($user['org_name']) : '<span class="text-muted">No organization assigned</span>' ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Organization Code</label>
                            <div class="fw-bold">
                                <?= $user['org_code'] ? esc($user['org_code']) : '<span class="text-muted">N/A</span>' ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-clock-history me-2"></i>Audit Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted">Created At</label>
                            <div class="fw-bold">
                                <?= date('F j, Y \a\t g:i A', strtotime($user['created_at'])) ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted">Last Updated</label>
                            <div class="fw-bold">
                                <?= date('F j, Y \a\t g:i A', strtotime($user['updated_at'])) ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label text-muted">Created By</label>
                            <div class="fw-bold">
                                <?= $user['created_by'] ? esc($user['created_by']) : '<span class="text-muted">System</span>' ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label text-muted">Position</label>
                    <div class="fw-bold">
                        <?= $user['position'] ? esc($user['position']) : '<span class="text-muted">Not specified</span>' ?>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label text-muted">ID Photo</label>
                    <div class="fw-bold">
                        <?php if ($user['id_photo']): ?>
                            <div class="mb-2">
                                <img src="<?= base_url($user['id_photo']) ?>" alt="ID Photo"
                                     class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                            </div>
                            <small class="text-muted"><?= esc(basename($user['id_photo'])) ?></small>
                        <?php else: ?>
                            <span class="text-muted">Not provided</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status & Permissions -->
<div class="col-md-4">
    <div class="card card-dark">
        <div class="card-header">
            <h5 class="card-title text-primary-custom mb-0">
                <i class="bi bi-shield-check me-2"></i>Status & Permissions
            </h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label text-muted">Role</label>
                <div>
                    <?php
                    $roleClass = $user['role'] === 'user' ? 'bg-success' : 'bg-warning';
                    ?>
                    <span class="badge <?= $roleClass ?> fs-6">
                        <?= ucfirst($user['role']) ?>
                    </span>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label text-muted">Status</label>
                <div>
                    <?php
                    $statusClass = match($user['status']) {
                        'active' => 'bg-success',
                        'inactive' => 'bg-secondary',
                        'suspended' => 'bg-danger',
                        default => 'bg-info'
                    };
                    ?>
                    <span class="badge <?= $statusClass ?> fs-6">
                        <?= ucfirst($user['status']) ?>
                    </span>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label text-muted">Administrator</label>
                <div>
                    <?php if ($user['is_admin']): ?>
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        <span class="text-success">Yes</span>
                    <?php else: ?>
                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                        <span class="text-danger">No</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label text-muted">Supervisor</label>
                <div>
                    <?php if ($user['is_supervisor']): ?>
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        <span class="text-success">Yes</span>
                    <?php else: ?>
                        <i class="bi bi-x-circle-fill text-danger me-2"></i>
                        <span class="text-danger">No</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Organization Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card card-dark">
            <div class="card-header">
                <h5 class="card-title text-primary-custom mb-0">
                    <i class="bi bi-building me-2"></i>Organization Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Organization Name</label>
                        <div class="fw-bold">
                            <?= $user['org_name'] ? esc($user['org_name']) : '<span class="text-muted">No organization assigned</span>' ?>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">Organization Code</label>
                        <div class="fw-bold">
                            <?= $user['org_code'] ? esc($user['org_code']) : '<span class="text-muted">N/A</span>' ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audit Information -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card card-dark">
            <div class="card-header">
                <h5 class="card-title text-primary-custom mb-0">
                    <i class="bi bi-clock-history me-2"></i>Audit Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">Created At</label>
                        <div class="fw-bold">
                            <?= date('F j, Y \a\t g:i A', strtotime($user['created_at'])) ?>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">Last Updated</label>
                        <div class="fw-bold">
                            <?= date('F j, Y \a\t g:i A', strtotime($user['updated_at'])) ?>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">Created By</label>
                        <div class="fw-bold">
                            <?= $user['created_by'] ? esc($user['created_by']) : '<span class="text-muted">System</span>' ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-end gap-2">
            <?= view('partials/back_button', [
                'href' => base_url('dakoii/system-users'),
                'label' => 'Back to Users List',
                'icon' => 'list',
                'class' => 'btn btn-secondary'
            ]) ?>
            <a href="<?= base_url('dakoii/system-users/' . $user['id'] . '/edit') ?>" class="btn btn-warning">
                <i class="bi bi-pencil me-2"></i>Edit User
            </a>
            <form method="post" action="<?= base_url('dakoii/system-users/' . $user['id'] . '/delete') ?>"
                  class="d-inline" onsubmit="return confirm('Are you sure you want to delete this user?')"
                <?= csrf_field() ?>
                <button type="submit" class="btn btn-danger">
                    <i class="bi bi-trash me-2"></i>Delete User
                </button>
            </form>
        </div>
    </div>
</div>

</div>
<?= $this->endSection() ?>
