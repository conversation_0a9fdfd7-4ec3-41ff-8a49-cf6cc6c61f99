<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-geo-alt me-2"></i><?= $title ?>
                <?php if ($filterEntity): ?>
                    <small class="text-muted">- <?= esc($filterEntity['business_name']) ?></small>
                <?php endif; ?>
            </h1>
            <p class="text-muted mb-0">Manage business locations and branches</p>
        </div>
        <div>
            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary me-2">
                <i class="bi bi-house me-1"></i>Back to Dashboard
            </a>
            <a href="<?= base_url('admin/business-locations/new' . ($entityFilter ? '?entity=' . $entityFilter : '')) ?>" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i>Add New Location
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-6">
                    <label for="entity" class="form-label">Filter by Business Entity</label>
                    <select class="form-select" id="entity" name="entity" onchange="this.form.submit()">
                        <option value="">All Business Entities</option>
                        <?php foreach ($entities as $entity): ?>
                            <option value="<?= $entity['id'] ?>" <?= $entityFilter == $entity['id'] ? 'selected' : '' ?>>
                                <?= esc($entity['business_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <?php if ($entityFilter): ?>
                        <a href="<?= base_url('admin/business-locations') ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>Clear Filter
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Business Locations Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="bi bi-list me-2"></i>Business Locations List
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($locations)): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" style="background-color: #f8f9fa;">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Location Name</th>
                                <th>Business Entity</th>
                                <th>Location</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($locations as $index => $location): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-info"><?= $index + 1 ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= esc($location['business_name']) ?></strong>
                                            <?php if ($location['remarks']): ?>
                                                <br><small class="text-muted"><?= esc($location['remarks']) ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (isset($location['entity_name'])): ?>
                                            <span class="badge bg-secondary">
                                                <?= esc($location['entity_name']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php
                                            $locationParts = array_filter([
                                                $location['district'],
                                                $location['province'],
                                                $location['country']
                                            ]);
                                            echo $locationParts ? esc(implode(', ', $locationParts)) : '-';
                                            ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = match($location['status']) {
                                            'active' => 'success',
                                            'inactive' => 'danger',
                                            default => 'secondary'
                                        };
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>">
                                            <?= ucfirst(esc($location['status'])) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M d, Y', strtotime($location['created_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('admin/business-locations/' . $location['id']) ?>" 
                                               class="btn btn-sm btn-outline-info" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="<?= base_url('admin/business-locations/' . $location['id'] . '/edit') ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete(<?= $location['id'] ?>, '<?= esc($location['business_name']) ?>')" 
                                                    title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="bi bi-geo-alt text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h6 class="text-muted">No Business Locations Found</h6>
                    <p class="text-muted small">
                        <?php if ($filterEntity): ?>
                            No locations found for <?= esc($filterEntity['business_name']) ?>.
                        <?php else: ?>
                            Start by creating your first business location.
                        <?php endif; ?>
                    </p>
                    <a href="<?= base_url('admin/business-locations/new' . ($entityFilter ? '?entity=' . $entityFilter : '')) ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Create First Location
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the business location "<span id="locationName"></span>"?</p>
                <p class="text-danger small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, name) {
    document.getElementById('locationName').textContent = name;
    document.getElementById('deleteForm').action = '<?= base_url('admin/business-locations') ?>/' + id + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>
