<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiUserModel extends Model
{
    protected $table = 'dakoii_users';
    protected $primaryKey = 'id';
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'name',
        'username',
        'password',
        'role',
        'is_active',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[255]',
        'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,{id}]',
        'password' => 'required|min_length[6]',
        'role' => 'required|max_length[100]',
        'is_active' => 'in_list[0,1]'
    ];
    
    protected $validationMessages = [
        'name' => [
            'required' => 'Name is required',
            'min_length' => 'Name must be at least 2 characters long',
            'max_length' => 'Name cannot exceed 255 characters'
        ],
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters long',
            'max_length' => 'Username cannot exceed 255 characters',
            'is_unique' => 'Username already exists'
        ],
        'password' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 6 characters long'
        ],

        'role' => [
            'required' => 'Role is required',
            'max_length' => 'Role cannot exceed 100 characters'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Hash password before saving
     */
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];
    
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        }
        
        return $data;
    }
    
    /**
     * Find user by username
     */
    public function findByUsername(string $username)
    {
        return $this->where('username', $username)
                   ->where('is_active', 1)
                   ->first();
    }
    
    /**
     * Verify user credentials
     */
    public function verifyCredentials(string $username, string $password)
    {
        $user = $this->findByUsername($username);

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return false;
    }
    
    /**
     * Get active users
     */
    public function getActiveUsers()
    {
        return $this->where('is_active', 1)
                   ->where('deleted_at', null)
                   ->findAll();
    }
    
    /**
     * Update user activity (updates the updated_at timestamp)
     */
    public function updateActivity(int $userId)
    {
        // Since useTimestamps = true, we just need to trigger an update
        // We'll update the updated_by field to trigger the timestamp update
        return $this->update($userId, ['updated_by' => $userId]);
    }
}
