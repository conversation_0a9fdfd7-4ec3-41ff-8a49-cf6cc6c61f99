<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\WorkplanModel;
use App\Models\ActivityModel;

class AdminPortal extends BaseController
{
    protected $userModel;
    protected $workplanModel;
    protected $activityModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->workplanModel = new WorkplanModel();
        $this->activityModel = new ActivityModel();
    }

    /**
     * Display admin login form
     */
    public function index()
    {
        // Check if already logged in
        if (session()->get('admin_logged_in')) {
            return redirect()->to('admin/dashboard');
        }

        $data = [
            'title' => 'Admin Portal Login'
        ];

        return view('admin_portal/admin_portal_login', $data);
    }

    /**
     * Handle admin authentication
     */
    public function authenticate()
    {
        // Check if request method is POST
        if (!$this->request->is('post')) {
            return redirect()->to('admin')->with('error', 'Invalid request method.');
        }

        // Get form data
        $email = trim($this->request->getPost('email'));
        $password = $this->request->getPost('password');
        $remember = $this->request->getPost('remember');

        // Basic validation
        if (empty($email) || empty($password)) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Please enter both email and password.');
        }

        // Verify user credentials (any user can login here)
        $user = $this->userModel->verifyCredentials($email, $password);

        if ($user) {
            // Check if account is active
            if ($user['status'] !== 'active') {
                return redirect()->back()
                               ->withInput()
                               ->with('error', 'Your account is not active. Please contact the administrator.');
            }

            // Check role first - if role is 'user', always redirect to field portal
            if ($user['role'] == 'user') {
                // User role is 'user', redirect to field portal regardless of admin/supervisor flags
                $sessionData = [
                    'field_user_id' => $user['id'],
                    'field_email' => $user['email'],
                    'field_name' => $user['name'],
                    'field_role' => $user['role'],
                    'field_is_admin' => $user['is_admin'],
                    'field_is_supervisor' => $user['is_supervisor'],
                    'field_org_id' => $user['org_id'],
                    'field_logged_in' => true
                ];

                session()->set($sessionData);

                // Handle remember me functionality
                if ($remember) {
                    // Set a longer session expiration (30 days)
                    session()->setTempdata('field_remember', true, 30 * 24 * 60 * 60);
                }

                // Redirect to field dashboard
                return redirect()->to('field/dashboard')->with('success', 'Welcome back, ' . $user['name'] . '!');
            }
            // If role is not 'user', check if user is admin or supervisor for admin portal access
            elseif ($user['is_admin'] == 1 || $user['is_supervisor'] == 1) {
                // Set admin session data
                $sessionData = [
                    'admin_user_id' => $user['id'],
                    'admin_email' => $user['email'],
                    'admin_name' => $user['name'],
                    'admin_role' => $user['role'],
                    'admin_is_admin' => $user['is_admin'],
                    'admin_is_supervisor' => $user['is_supervisor'],
                    'admin_org_id' => $user['org_id'],
                    'admin_logged_in' => true
                ];

                session()->set($sessionData);

                // Handle remember me functionality
                if ($remember) {
                    // Set a longer session expiration (30 days)
                    session()->setTempdata('admin_remember', true, 30 * 24 * 60 * 60);
                }

                // Redirect to admin dashboard
                return redirect()->to('admin/dashboard')->with('success', 'Welcome back, ' . $user['name'] . '!');
            } else {
                // User has no admin/supervisor privileges and role is not 'user', redirect to field portal
                $sessionData = [
                    'field_user_id' => $user['id'],
                    'field_email' => $user['email'],
                    'field_name' => $user['name'],
                    'field_role' => $user['role'],
                    'field_is_admin' => $user['is_admin'],
                    'field_is_supervisor' => $user['is_supervisor'],
                    'field_org_id' => $user['org_id'],
                    'field_logged_in' => true
                ];

                session()->set($sessionData);

                // Handle remember me functionality
                if ($remember) {
                    // Set a longer session expiration (30 days)
                    session()->setTempdata('field_remember', true, 30 * 24 * 60 * 60);
                }

                // Redirect to field dashboard
                return redirect()->to('field/dashboard')->with('success', 'Welcome back, ' . $user['name'] . '!');
            }
        } else {
            // Authentication failed
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Invalid email or password.');
        }
    }

    /**
     * Display admin dashboard
     */
    public function dashboard()
    {
        // Check if logged in
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the admin portal.');
        }

        // Get organization ID for filtering
        $orgId = session()->get('admin_org_id');

        // Get statistics for the organization
        $workplanStats = $this->getWorkplanStats($orgId);
        $activityStats = $this->getActivityStats($orgId);

        // Get recent workplans and activities
        $recentWorkplans = $this->getRecentWorkplans($orgId, 5);
        $recentActivities = $this->getRecentActivities($orgId, 5);

        $data = [
            'title' => 'Admin Dashboard',
            'user' => [
                'id' => session()->get('admin_user_id'),
                'name' => session()->get('admin_name'),
                'email' => session()->get('admin_email'),
                'role' => session()->get('admin_role'),
                'is_admin' => session()->get('admin_is_admin'),
                'is_supervisor' => session()->get('admin_is_supervisor')
            ],
            'stats' => [
                'total_workplans' => $workplanStats['total'],
                'active_workplans' => $workplanStats['active'],
                'total_activities' => $activityStats['total'],
                'active_activities' => $activityStats['active'],
                'submitted_activities' => $activityStats['submitted'],
                'approved_activities' => $activityStats['approved']
            ],
            'recent_workplans' => $recentWorkplans,
            'recent_activities' => $recentActivities
        ];

        return view('admin_portal/admin_portal_dashboard', $data);
    }

    /**
     * Get workplan statistics for organization
     */
    private function getWorkplanStats($orgId)
    {
        $total = $this->workplanModel->where('org_id', $orgId)
                                   ->where('is_deleted', 0)
                                   ->countAllResults();

        $active = $this->workplanModel->where('org_id', $orgId)
                                    ->where('is_deleted', 0)
                                    ->where('status', 'active')
                                    ->countAllResults();

        return [
            'total' => $total,
            'active' => $active
        ];
    }

    /**
     * Get activity statistics for organization
     */
    private function getActivityStats($orgId)
    {
        $total = $this->activityModel->where('org_id', $orgId)
                                   ->where('is_deleted', 0)
                                   ->countAllResults();

        $active = $this->activityModel->where('org_id', $orgId)
                                    ->where('is_deleted', 0)
                                    ->where('status', 'active')
                                    ->countAllResults();

        $submitted = $this->activityModel->where('org_id', $orgId)
                                       ->where('is_deleted', 0)
                                       ->where('status', 'submitted')
                                       ->countAllResults();

        $approved = $this->activityModel->where('org_id', $orgId)
                                      ->where('is_deleted', 0)
                                      ->where('status', 'approved')
                                      ->countAllResults();

        return [
            'total' => $total,
            'active' => $active,
            'submitted' => $submitted,
            'approved' => $approved
        ];
    }

    /**
     * Get recent workplans for organization
     */
    private function getRecentWorkplans($orgId, $limit = 5)
    {
        return $this->workplanModel->where('org_id', $orgId)
                                  ->where('is_deleted', 0)
                                  ->orderBy('created_at', 'DESC')
                                  ->limit($limit)
                                  ->find();
    }

    /**
     * Get recent activities for organization
     */
    private function getRecentActivities($orgId, $limit = 5)
    {
        return $this->activityModel->select('activities.*, workplans.title as workplan_title')
                                  ->join('workplans', 'workplans.id = activities.workplan_id', 'left')
                                  ->where('activities.org_id', $orgId)
                                  ->where('activities.is_deleted', 0)
                                  ->orderBy('activities.created_at', 'DESC')
                                  ->limit($limit)
                                  ->find();
    }

    /**
     * Display forgot password form
     */
    public function forgotPassword()
    {
        $data = [
            'title' => 'Forgot Password - Admin Portal'
        ];

        return view('admin_portal/admin_portal_forgot_password', $data);
    }

    /**
     * Handle forgot password request
     */
    public function processForgotPassword()
    {
        // Check if request method is POST
        if (!$this->request->is('post')) {
            return redirect()->to('admin/forgot-password')->with('error', 'Invalid request method.');
        }

        $email = trim($this->request->getPost('email'));

        // Basic validation
        if (empty($email)) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', 'Please enter your email address.');
        }

        // Find user by email (admin or supervisor only)
        $user = $this->userModel->getAdminOrSupervisorByEmail($email);

        if (!$user) {
            // Don't reveal if email exists or not for security
            return redirect()->back()
                           ->with('success', 'If your email is registered as an admin/supervisor account, you will receive password reset instructions.');
        }

        // Generate temporary password and activate account if pending
        $tempPassword = $this->userModel->generateTemporaryPasswordForAdmin($user['id']);
        
        // If account is pending, activate it
        if ($user['status'] === 'pending') {
            $this->userModel->update($user['id'], ['status' => 'active']);
        }

        // Send email with temporary password
        $emailSent = $this->sendTemporaryPasswordEmail($user['email'], $user['name'], $tempPassword);

        if ($emailSent) {
            return redirect()->to('admin')
                           ->with('success', 'A temporary password has been sent to your email address. Please check your inbox.');
        } else {
            return redirect()->back()
                           ->with('error', 'Failed to send email. Please try again or contact the administrator.');
        }
    }

    /**
     * Logout admin user
     */
    public function logout()
    {
        // Remove admin session data
        $adminSessionKeys = [
            'admin_user_id',
            'admin_email', 
            'admin_name',
            'admin_role',
            'admin_is_admin',
            'admin_is_supervisor',
            'admin_org_id',
            'admin_logged_in',
            'admin_remember'
        ];

        foreach ($adminSessionKeys as $key) {
            session()->remove($key);
        }

        return redirect()->to('admin')->with('success', 'You have been logged out successfully.');
    }

    /**
     * Send temporary password email
     */
    private function sendTemporaryPasswordEmail(string $email, string $name, string $tempPassword): bool
    {
        $emailService = \Config\Services::email();

        $subject = 'PCOLLX Admin Portal - Temporary Password';
        $message = "
        <html>
        <head>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 0;
                    background-color: #f8f9fa;
                }
                .email-container {
                    max-width: 650px;
                    margin: 20px auto;
                    background-color: #ffffff;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }
                .header {
                    background: linear-gradient(135deg, #00B4D8 0%, #0077B6 100%);
                    color: white;
                    padding: 30px 20px;
                    text-align: center;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                    font-weight: 300;
                }
                .header p {
                    margin: 5px 0 0 0;
                    font-size: 14px;
                    opacity: 0.9;
                }
                .content {
                    padding: 40px 30px;
                    background-color: #ffffff;
                }
                .password-display {
                    background: #f8f9fa;
                    border: 2px solid #00B4D8;
                    border-radius: 8px;
                    padding: 15px;
                    text-align: center;
                    font-size: 24px;
                    font-weight: bold;
                    color: #0077B6;
                    letter-spacing: 3px;
                    margin: 20px 0;
                }
                .footer {
                    background-color: #f8f9fa;
                    padding: 20px;
                    text-align: center;
                    border-top: 1px solid #dee2e6;
                }
                .btn {
                    display: inline-block;
                    padding: 12px 30px;
                    background: linear-gradient(45deg, #00B4D8, #0077B6);
                    color: white;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: 500;
                    margin: 20px 0;
                }
            </style>
        </head>
        <body>
            <div class='email-container'>
                <div class='header'>
                    <h1>🔑 Temporary Password</h1>
                    <p>PCOLLX Admin Portal</p>
                </div>
                <div class='content'>
                    <p>Hello <strong>{$name}</strong>,</p>
                    <p>You have requested a password reset for your PCOLLX Admin Portal account. Your temporary password is:</p>

                    <div class='password-display'>{$tempPassword}</div>

                    <p><strong>Important Security Notes:</strong></p>
                    <ul>
                        <li>This is a temporary 4-digit password</li>
                        <li>Please change your password after logging in</li>
                        <li>This password will expire after first use</li>
                        <li>If you didn't request this, please contact your administrator</li>
                    </ul>

                    <div style='text-align: center;'>
                        <a href='" . base_url('admin') . "' class='btn'>Login to Admin Portal</a>
                    </div>

                    <p>If you experience any issues logging in, please contact your system administrator.</p>
                </div>
                <div class='footer'>
                    <p><strong>PCOLLX - Price Collection System</strong></p>
                    <p>Admin Portal Access</p>
                    <p>© 2025 Dakoii Systems. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        ";

        $emailService->setTo($email);
        $emailService->setSubject($subject);
        $emailService->setMessage($message);

        return $emailService->send();
    }
}
