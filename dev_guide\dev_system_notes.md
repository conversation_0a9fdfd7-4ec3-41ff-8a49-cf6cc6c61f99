# Price Data Collection System Architecture

## Architectural Foundation and Design Philosophy

The Price Data Collection System architecture is built upon CodeIgniter 4's enhanced MVC framework, designed to support a complex multi-organizational price surveillance operation across Papua New Guinea's diverse economic landscape. The architecture embraces a domain-driven design approach where each of the three portals (Dakoii, Admin, and Field) represents distinct bounded contexts with their own specific responsibilities, user experiences, and operational requirements.

The architectural philosophy centers on creating a scalable, maintainable system that can handle the complexities of multi-organizational data collection while ensuring data integrity, security, and operational efficiency. The system must accommodate various user types from super-administrators managing national-level operations to field officers collecting price data in remote locations with limited connectivity.

The RESTful architectural approach ensures that every API endpoint serves a single, well-defined purpose with clear HTTP method semantics. This strict adherence to REST principles creates predictable interfaces that support both human users through web interfaces and potential future integrations with external systems such as government databases, financial institutions, or research organizations.

## Database Architecture and MySQL Schema Design

### Core Organizational Structure

The database architecture begins with the foundational organizational hierarchy that supports multi-organizational operations. The `dakoii_organizations` table serves as the root entity, containing organizations such as government departments, NGOs, or commercial entities responsible for price surveillance programs. Each organization maintains its own operational scope while participating in the broader surveillance network.

The geographic hierarchy implements Papua New Guinea's administrative structure through interconnected tables for countries, provinces, and districts. This geographic framework supports both operational assignment of field officers and analytical reporting across different administrative boundaries. The hierarchical design ensures that price data can be aggregated and analyzed at multiple geographic levels while maintaining referential integrity throughout the system.

User management employs a sophisticated role-based access control system through the `users` table connected to role assignment tables that define permissions within organizational contexts. The four-tier user hierarchy (Super Admin, Admin, Supervisor, User, Guest) implements graduated access levels where each role inherits appropriate permissions while maintaining strict organizational boundaries to prevent unauthorized cross-organizational data access.

### Goods Classification and Product Hierarchy

The goods management system implements the three-tier classification structure evident in your operational data from Lae City surveillance activities. The `goods_groups` table establishes broad categories such as "Rice" or "Cooking Oil" that provide the organizational framework for market analysis and reporting.

The `goods_brands` table introduces the critical primary versus substitute classification that supports sophisticated market analysis including price elasticity studies and consumer choice modeling. Each brand record includes classification flags that determine whether the brand represents a primary market option or serves as a substitute product, enabling comprehensive competitive analysis.

The `goods_items` table represents the most granular level of product specification, containing the actual products that field officers encounter during data collection. Using the rice example from your surveillance data, this table would contain entries for "Roots Rice 20kg," "Roots Rice 10kg," "Roots Rice 5kg," "Roots Rice 1kg," and "Roots Rice 500g." Each item maintains foreign key relationships to both brands and groups while including specific attributes such as package size, unit of measure, and other characteristics relevant to price collection and analysis.

### Business Entity and Location Management

The business entity structure accommodates the operational reality that commercial establishments often operate multiple physical locations. The `entities` table captures core business information including business registration details, ownership information, and operational characteristics that affect pricing strategies.

The `entity_locations` table maintains detailed information about each physical location where price data collection occurs. Drawing from your Lae City example, Papindo would exist as a single entity with multiple location records for Wewak Town and Wewak Dagua operations. Each location record includes specific address information, operational hours, manager contact details, and location-specific characteristics that may influence pricing patterns.

This separation enables sophisticated analysis of price variations not only across different business types but also across different locations operated by the same business entity. The system can identify whether price differences reflect transportation costs, local competition, operational efficiency, or other market factors through comparative analysis of multi-location entities.

### Exercise and Workflow Management Schema

The exercise management system translates strategic planning into executable field operations through a hierarchical structure that mirrors project management best practices. The `exercises` table captures high-level surveillance campaigns with defined objectives, geographic scope, time periods, and resource allocations.

The `workplans` table provides intermediate organizational structure within exercises, allowing administrators to break down large surveillance operations into manageable components. Each workplan defines specific objectives, target geographic areas, participating organizations, and timeline constraints that guide operational planning.

The `activities` table breaks down workplans into specific operational components, with price collection being one of several activity types the system supports. Activities include detailed specifications about what data should be collected, which business types should be surveyed, and what analytical objectives the activity supports.

The `tasks` table represents the most granular level of operational assignment, containing specific instructions that individual field officers can execute within defined timeframes. Each task includes detailed specifications about target locations, specific goods to be surveyed, data collection requirements, and quality standards that must be met.

Status tracking throughout these workflow tables enables comprehensive monitoring of operational progress from initial planning through final data approval and integration. The status fields support workflow state management that ensures proper progression through approval processes while maintaining accountability for all data collection activities.

### Price Data Collection and Storage

The price data collection schema captures not only the numerical price information but also the extensive contextual metadata necessary for analytical validity and operational accountability. The `price_collections` table serves as the central repository for all price observations, with foreign key relationships that connect each price point to specific goods items, business locations, collection periods, and responsible officers.

Temporal tracking includes multiple timestamp fields that capture when prices were observed in the market, when data was entered into the system, and when the data was approved for analytical use. This temporal granularity supports quality assurance processes, trend analysis, and operational performance monitoring.

The schema accommodates both retail and wholesale price tracking as evidenced in your surveillance data, with classification fields that distinguish between price types while maintaining consistent data structure. Additional fields capture market conditions, promotional activities, stock availability, and other contextual factors that influence pricing and market dynamics.

Quality assurance fields throughout the price collection schema support the multi-level approval process that ensures data reliability. These fields track validation status, approval workflows, data quality flags, and supervisor review comments that maintain accountability throughout the data collection and approval process.

## CodeIgniter 4 MVC Architecture Implementation

### Model Layer Design and Business Logic

The model layer implements comprehensive business logic that serves all three portals while maintaining consistency in data validation, processing, and storage operations. Models encapsulate complex business rules around price data validation, workflow state management, and user permission enforcement, ensuring that business logic remains centralized and consistent regardless of which portal initiates operations.

The geographical models implement the hierarchical location structure with methods that support both operational assignment of field officers and analytical reporting across administrative boundaries. These models include validation logic that ensures geographic assignments remain within appropriate organizational scope while supporting flexible reporting requirements across different administrative levels.

Goods management models implement the three-tier classification system with sophisticated validation logic that maintains referential integrity throughout the product hierarchy. The models include business rules that prevent orphaned goods items, ensure consistent classification standards, and support the complex queries required for analytical reporting across different product categories and brand classifications.

User management models implement role-based access control with organizational boundary enforcement that prevents unauthorized cross-organizational access while supporting the complex permission requirements of your four-tier user hierarchy. These models include authentication logic, session management, and permission validation that secures all system operations.

Workflow management models orchestrate the complex state transitions required for exercise planning, task assignment, and approval processes. These models include business logic that ensures proper workflow progression, validates assignment constraints, and maintains accountability throughout the operational lifecycle.

Price data models implement comprehensive validation logic that ensures data quality while accommodating the operational realities of field collection under challenging conditions. These models include range validation, consistency checking, and historical comparison logic that identifies potential data quality issues while maintaining flexibility for legitimate market variations.

### Controller Architecture and RESTful Implementation

The controller layer implements strict RESTful principles with dedicated controllers for each resource type and clear separation between HTTP methods. Each controller method serves a single HTTP verb and performs a single type of operation, creating predictable interfaces that support both human users and potential automated integrations.

Authentication controllers handle user login, token refresh, and session management using JSON Web Tokens that provide secure, stateless authentication suitable for the multi-portal architecture. Separate methods handle POST requests for login operations, GET requests for session validation, and DELETE requests for logout operations, maintaining clear separation of concerns.

Geographic resource controllers provide comprehensive location management with GET methods for retrieving hierarchical location data, POST methods for creating new geographic entries, PUT methods for complete location updates, and DELETE methods for removing obsolete locations. The controllers implement efficient caching strategies that reduce database load while ensuring geographic data remains current.

Goods management controllers support the complete lifecycle of product hierarchy management with separate methods for each HTTP verb and resource type. GET methods retrieve goods information with flexible filtering and sorting options, POST methods create new goods entries with comprehensive validation, PUT methods handle complete goods record replacement, PATCH methods manage partial updates like classification changes, and DELETE methods remove obsolete goods while maintaining referential integrity.

Entity management controllers handle business location registration and maintenance with methods that accommodate the complex relationship between business entities and their multiple operational locations. The controllers include validation logic that ensures location assignments remain within appropriate geographic and organizational boundaries.

Workflow management controllers implement the complete exercise lifecycle from initial creation through task completion and approval. These controllers recognize the hierarchical nature of the workflow structure with separate endpoints for exercises, workplans, activities, and tasks, each supporting appropriate CRUD operations based on user permissions and workflow state.

Price data controllers handle the core surveillance functionality with robust validation and error handling that accommodates field collection challenges. GET methods retrieve price data with flexible filtering for analytical purposes, POST methods accept price submissions with comprehensive validation, PUT methods handle corrections to existing price data, and PATCH methods support partial updates during approval processes.

### View Layer and Portal-Specific Interface Design

The view layer adapts to the distinct requirements of each portal while maintaining consistent underlying data presentation logic and shared component libraries that ensure system coherence. Each portal implements its own view namespace with templates optimized for specific user roles and operational contexts.

Dakoii Portal views emphasize administrative clarity and comprehensive data presentation that supports super-administrators who need detailed system information for organizational oversight and strategic planning. These views include complex dashboard interfaces, detailed reporting screens, and administrative tools that provide comprehensive system visibility.

Admin Portal views focus on workflow management and operational dashboards that support complex coordination tasks including task assignment, progress monitoring, and approval workflows. These views implement sophisticated data visualization that helps administrators understand operational status, identify bottlenecks, and optimize resource allocation.

Field Portal views prioritize simplicity and mobile optimization with streamlined interfaces that function effectively under challenging field conditions. These views implement responsive design principles that adapt to various screen sizes while maintaining functionality, efficient data entry forms that minimize input requirements, and offline capabilities that ensure operational continuity during connectivity interruptions.

## RESTful API Architecture and Endpoint Structure

### Authentication and Security Endpoints

The authentication system implements comprehensive security endpoints that support the complex organizational and role-based requirements of the multi-organizational surveillance system. The `/api/auth/login` POST endpoint handles user authentication with organizational context validation, returning JWT tokens that include organizational scope and role permissions.

Token management endpoints include `/api/auth/refresh` POST for token renewal, `/api/auth/validate` GET for token verification, and `/api/auth/logout` DELETE for session termination. These endpoints implement security measures that prevent token hijacking while accommodating the operational requirements of field officers who may need extended authenticated sessions.

Password management endpoints support secure credential changes through `/api/auth/password` PUT for complete password replacement and `/api/auth/password/reset` POST for password recovery workflows. These endpoints implement security measures that prevent unauthorized access while providing legitimate users with reliable password management capabilities.

### Geographic Resource Management

Geographic endpoints provide comprehensive location hierarchy management through resource-oriented interfaces that support both operational assignment and analytical reporting requirements. The `/api/countries` endpoint supports GET operations for retrieving country lists with optional filtering and sorting parameters.

Province management through `/api/provinces` includes GET operations for retrieving provinces with country filtering, POST operations for creating new provinces with validation against country boundaries, PUT operations for complete province record updates, and DELETE operations for removing obsolete provinces with referential integrity checking.

District management endpoints follow similar patterns with `/api/districts` supporting comprehensive CRUD operations with appropriate validation and filtering capabilities. These endpoints implement efficient caching strategies that reduce database load while ensuring geographic data remains current for operational purposes.

### Goods Classification Management

Goods management endpoints implement the three-tier classification system through separate resource endpoints that maintain hierarchical relationships while supporting independent management of each classification level. The `/api/goods-groups` endpoint supports comprehensive group management with GET operations for retrieval, POST for creation, PUT for updates, and DELETE for removal with referential integrity protection.

Brand management through `/api/goods-brands` includes additional complexity for handling primary versus substitute classifications with endpoints that support classification queries, bulk classification updates, and analytical reporting on classification distributions. The endpoints include validation logic that ensures classification consistency and prevents orphaned brand records.

Goods item management endpoints at `/api/goods-items` support the most complex operations including hierarchical validation, specification management, and analytical queries that support operational reporting and strategic planning. These endpoints implement sophisticated validation logic that ensures item specifications remain consistent with brand and group classifications.

### Business Entity and Location Management

Entity management endpoints support the complex relationship between business entities and their multiple operational locations through carefully designed resource hierarchies. The `/api/entities` endpoint supports standard CRUD operations with additional complexity for handling multi-location businesses.

Location management through `/api/entities/{id}/locations` implements nested resource patterns that maintain proper relationships between entities and their locations. These endpoints support location-specific operations while maintaining entity-level coordination and validation.

Business classification endpoints support analytical requirements through `/api/entities/classifications` with GET operations that provide business type distributions, geographic distributions, and operational characteristic analysis that supports strategic planning and resource allocation.

### Workflow and Exercise Management

Exercise management endpoints support the complete surveillance campaign lifecycle through `/api/exercises` with comprehensive CRUD operations that include complex validation for organizational scope, resource allocation, and timeline constraints. GET operations support flexible filtering and sorting for operational monitoring and strategic planning.

Workplan management through `/api/exercises/{id}/workplans` implements nested resource patterns that maintain proper hierarchical relationships while supporting independent workplan management. These endpoints include validation logic that ensures workplan objectives align with exercise goals while maintaining operational flexibility.

Activity management endpoints at `/api/workplans/{id}/activities` support the breakdown of workplans into executable components with validation that ensures activity specifications remain achievable within resource and timeline constraints.

Task management through `/api/activities/{id}/tasks` implements the most granular level of operational management with endpoints that support assignment workflows, progress tracking, and approval processes. These endpoints include sophisticated state management that ensures proper workflow progression while maintaining accountability.

### Price Data Collection and Management

Price data endpoints implement the core surveillance functionality through `/api/price-collections` with robust validation and error handling that accommodates field collection challenges while maintaining data quality standards. GET operations support flexible filtering for analytical purposes including temporal ranges, geographic boundaries, goods categories, and business entity types.

Price submission endpoints accept data from field officers through POST operations with comprehensive validation that checks data completeness, range validation, consistency with historical patterns, and compliance with collection standards. The endpoints implement batch submission capabilities that allow efficient data transfer when connectivity permits.

Price correction endpoints support data quality assurance through PUT and PATCH operations that handle corrections identified during approval processes. These endpoints maintain audit trails that track all data modifications while supporting the multi-level approval workflows required for data quality assurance.

### Reporting and Analytics Endpoints

Reporting endpoints provide read-only access to analytical data through `/api/reports` with flexible parameter support that accommodates various analytical requirements. Market analysis endpoints generate reports on price trends, geographic variations, and business entity comparisons with configurable time periods and geographic scope.

Operational reporting endpoints at `/api/reports/operations` provide insights into data collection performance including officer productivity, task completion rates, data quality indicators, and workflow efficiency measures. These reports support management decision-making regarding resource allocation and process improvements.

Export endpoints support various analytical requirements through `/api/exports` with multiple format options including CSV for spreadsheet analysis, JSON for system integration, and PDF for presentation purposes. The export system includes scheduling capabilities that support regular data transfers to authorized external systems.

## Security Architecture and Access Control Implementation

### Multi-Level Authentication and Authorization

The security architecture implements defense-in-depth principles that protect sensitive price surveillance data while accommodating operational realities of field data collection across diverse geographic and infrastructure conditions. The authentication system uses industry-standard JWT implementation with organizational context embedding that ensures tokens carry appropriate scope limitations.

Role-based access control extends beyond simple permission checking to implement complex organizational boundary enforcement that prevents unauthorized cross-organizational access while supporting legitimate inter-organizational coordination where appropriate. The authorization system validates not only user roles but also organizational context, geographic scope, and data classification levels.

Session management accommodates the operational requirements of field officers who may need extended authenticated sessions while operating in areas with intermittent connectivity. The system implements token refresh mechanisms that maintain security while supporting operational continuity, with appropriate expiration policies that balance security requirements against operational convenience.

### Data Protection and Encryption

Data encryption protects sensitive information both in transit and at rest using established encryption standards that comply with government requirements for economic data protection. The encryption implementation covers not only price data but also user information, organizational details, and operational metadata while avoiding excessive performance overhead.

Database encryption employs field-level encryption for highly sensitive data including user credentials, organizational identifiers, and certain types of market data that require additional protection. The encryption key management system implements appropriate separation of concerns that ensures encryption keys remain secure while supporting operational access requirements.

Network security implements HTTPS enforcement for all communications with additional certificate validation that prevents man-in-the-middle attacks. The network security architecture includes appropriate firewalling and intrusion detection that protects against unauthorized access attempts while supporting legitimate operational traffic.

### Input Validation and Attack Prevention

Input validation occurs at multiple levels throughout the system beginning with client-side validation that provides immediate user feedback and continuing through comprehensive server-side validation that ensures data integrity regardless of request origin. The validation system implements appropriate sanitization that prevents injection attacks while supporting legitimate data entry requirements.

SQL injection prevention employs CodeIgniter 4's query builder and prepared statement systems with additional validation layers that ensure query parameters remain within expected bounds. The database interaction layer includes appropriate input sanitization and validation that prevents malicious queries while supporting complex analytical requirements.

Cross-site scripting prevention implements appropriate output encoding and content security policies that prevent malicious script injection while supporting the dynamic content requirements of the dashboard and reporting interfaces. The XSS prevention system includes validation of user-generated content and appropriate escaping of dynamic output.

## Performance Optimization and Scalability Architecture

### Database Performance and Query Optimization

Database performance optimization employs strategic indexing that accelerates common query patterns while avoiding index proliferation that could impact data modification performance. Primary indexes support the foreign key relationships essential to the normalized database design while secondary indexes optimize reporting queries used by administrators and supervisors.

Query optimization focuses on the complex analytical queries required for dashboard and reporting functionality, ensuring efficient execution even as price data volumes grow significantly. The optimization approach includes strategic use of database views for complex analytical operations, careful attention to join patterns that minimize computational overhead, and query plan analysis that identifies performance bottlenecks.

Connection pooling and database resource management ensure efficient database utilization under varying load conditions including peak periods when multiple field officers submit data simultaneously and intensive analytical periods when complex reports are generated.

### Caching Strategy and Implementation

Caching strategies leverage CodeIgniter 4's flexible caching system to reduce database load for frequently accessed information that changes infrequently. Geographic hierarchy data, goods classification information, and user role definitions benefit from aggressive caching policies that improve response times while ensuring data consistency.

Application-level caching implements intelligent cache invalidation that updates cached information promptly when underlying data changes while maintaining cache coherence across multiple application instances. The caching system includes appropriate cache warming strategies that ensure frequently accessed data remains available for rapid retrieval.

API response caching implements efficient serialization and compression strategies that reduce bandwidth requirements, particularly important for field officers operating in low-bandwidth environments. The caching includes selective field loading that allows clients to request only required data fields, reducing both server processing requirements and network transfer times.

### Load Balancing and Scalability Planning

Load balancing architecture supports horizontal scaling that can accommodate increasing user loads and data volumes as surveillance operations expand across Papua New Guinea. The load balancing implementation includes session affinity management that ensures consistent user experiences while distributing load efficiently across multiple application instances.

Database scaling strategies include read replica implementation that distributes analytical query load while maintaining data consistency for transactional operations. The scaling architecture supports geographic distribution of database resources that can reduce latency for field officers operating in different regions.

Application scaling includes containerization strategies that support efficient deployment and scaling of application instances based on demand patterns. The scaling architecture includes appropriate monitoring and automated scaling triggers that ensure system performance remains consistent under varying load conditions.

## Integration Architecture and External System Connectivity

### API Versioning and External Integration Support

API versioning implements semantic versioning that ensures external integrations remain stable while the system continues to evolve and add new functionality. The versioning strategy includes backward compatibility commitments that protect existing integrations while providing clear migration paths for significant architectural changes.

External system integration supports various stakeholder requirements including government databases that require economic surveillance data, financial institutions that use price data for economic analysis, and research organizations that conduct market studies. The integration architecture includes appropriate authentication and authorization that ensures external access remains secure and auditable.

Data synchronization capabilities support real-time and batch integration patterns with external systems including webhook implementations that provide event-driven notifications and scheduled export capabilities that support regular data transfers to authorized external systems.

### Export and Import Functionality

Data export capabilities support various analytical and integration requirements through multiple format options including CSV for spreadsheet analysis, JSON for system integration, XML for legacy system compatibility, and specialized formats for economic analysis tools. The export system includes appropriate data filtering and transformation that ensures exported data meets specific external system requirements.

Import functionality supports data integration from external sources including geographic data updates from government systems, business entity information from commercial databases, and supplementary market data from partner organizations. The import system includes appropriate validation and conflict resolution that ensures imported data maintains system integrity.

Batch processing capabilities support large-scale data operations including historical data migration, bulk corrections identified through quality assurance processes, and periodic data maintenance operations that ensure system performance remains optimal.

## Deployment Architecture and Infrastructure Requirements

### Server Architecture and Infrastructure Planning

Server architecture recommendations focus on proven technology stacks that provide reliable performance while minimizing administrative overhead and operational complexity. The recommended deployment includes load balancing capabilities that accommodate traffic growth, database replication that provides both performance improvements and disaster recovery capabilities, and monitoring systems that provide early warning of potential issues.

Infrastructure requirements account for the geographic distribution of users across Papua New Guinea with consideration for network latency, bandwidth limitations, and infrastructure reliability variations across different regions. The deployment architecture includes appropriate content delivery strategies that ensure acceptable performance for field officers operating in challenging connectivity environments.

Security infrastructure includes appropriate firewalling, intrusion detection, and monitoring that protects against unauthorized access while supporting legitimate operational requirements. The security infrastructure includes regular security scanning and vulnerability assessment that ensures ongoing protection against evolving threats.

### Backup and Disaster Recovery

Backup strategies ensure comprehensive protection of critical price surveillance data against various failure scenarios including hardware failures, software corruption, natural disasters, and human error. The backup system includes automated daily incremental backups, weekly full system backups, and monthly archival backups that provide multiple recovery options.

Disaster recovery procedures provide systematic approaches for restoring operations following various failure scenarios with tested recovery procedures that minimize downtime and data loss. The disaster recovery system includes geographically distributed backup storage, documented recovery procedures, and regular recovery testing that ensures procedures remain current and effective.

Data retention policies implement appropriate lifecycle management for surveillance data including operational data retention that supports ongoing analysis requirements, archival policies that ensure historical data remains available for long-term studies, and secure disposal procedures for data that has exceeded retention requirements.

### Monitoring and Maintenance Procedures

Performance monitoring implements comprehensive system observation that tracks application performance, database efficiency, network utilization, and user experience metrics. The monitoring system includes appropriate alerting that provides early warning of performance degradation or system failures while avoiding alert fatigue through intelligent threshold management.

Application monitoring includes error tracking that identifies and categorizes system errors, user behavior analysis that supports system optimization, and performance profiling that identifies optimization opportunities. The monitoring system includes appropriate dashboard interfaces that provide system administrators with actionable insights into system performance and utilization patterns.

Maintenance procedures provide systematic approaches for deploying updates, applying security patches, and implementing system improvements while minimizing service disruption. The maintenance system includes staging environments that allow thorough testing before production deployment, rollback capabilities that provide quick recovery from problematic updates, and communication protocols that keep users informed about system changes and maintenance schedules.

This comprehensive architectural foundation ensures that the Price Data Collection System provides reliable, secure, and scalable service that supports Papua New Guinea's economic monitoring requirements while maintaining the flexibility to adapt to changing operational needs and expanding surveillance scope across the country's diverse economic landscape.


System Architecture Document for Price Data Collection System
This document outlines the system architecture for the Price Data Collection System, built using CodeIgniter 4 and MySQL. The system is designed to manage price surveillance data for goods across multiple business entities, with three distinct portals: Dakoii Portal (Super Admin), Admin Portal, and Field Portal. The architecture ensures scalability, modularity, and efficient handling of low-bandwidth environments for field users.

1. System Overview
The Price Data Collection System is a web-based application designed to facilitate the collection, management, and analysis of price data for essential goods across various business houses. It supports:

Dakoii Portal: Managed by super admins to create organizations, geographical locations, and organization users (admins).
Admin Portal: Used by admins and supervisors to manage goods, business entities, workplans, activities, tasks, approvals, and reports.
Field Portal: A lightweight interface for field officers to complete assigned price collection tasks, optimized for low-bandwidth environments.

The system uses CodeIgniter 4 as the PHP framework for rapid development, MVC architecture, and RESTful APIs, with MySQL as the relational database for data persistence.

2. System Architecture
The architecture follows a three-tier architecture (Presentation, Application, Data) with modular components to ensure maintainability and scalability. Below is a detailed breakdown:
2.1. Presentation Layer
The presentation layer provides user interfaces for the three portals, built with responsive front-end technologies to ensure accessibility and usability.

Technologies:

HTML5, CSS3, JavaScript: For responsive and interactive UI.
Bootstrap 5: For consistent, mobile-friendly design.
jQuery: For DOM manipulation and AJAX requests.
DataTables: For displaying tabular data (e.g., price lists, task assignments).
Minimalist Design for Field Portal: Lightweight CSS and minimal JavaScript to optimize performance in low-bandwidth areas.


Portals:

Dakoii Portal:
Features: Organization management, geographical location setup (province, country, districts), user role assignments (admin).
UI Components: Forms for creating organizations and users, tables for viewing and managing data.


Admin Portal:
Features: Goods management, business house registration, workplan creation, task assignment, task approval, report generation.
UI Components: Dashboard with analytics, forms for goods and entity management, task creation and approval workflows, report visualizations.


Field Portal:
Features: Task viewing, price data submission, offline data caching (if needed).
UI Components: Minimalist forms for price data entry, task status indicators, optimized for mobile devices.





2.2. Application Layer
The application layer, built with CodeIgniter 4, handles business logic, API endpoints, and request routing. It follows the MVC pattern to separate concerns.

CodeIgniter 4 Components:

Controllers:
DakoiiController: Manages organization and user creation, geographical data setup.
AdminController: Handles goods, entities, workplans, activities, tasks, and reports.
FieldController: Manages task retrieval and price data submission for field officers.
AuthController: Manages user authentication and role-based access control (RBAC).


Models:
OrganizationModel: Manages Dakoii organizations.
UserModel: Handles user data (admin, supervisor, user, guest).
GoodsModel: Manages goods groups, brands, and items.
EntityModel: Manages business houses and their locations.
WorkplanModel: Manages workplans, activities, and tasks.
PriceDataModel: Stores price surveillance data.


Libraries/Services:
Authentication: Uses CodeIgniter’s built-in session management and custom RBAC logic.
Validation: Ensures data integrity for price submissions and entity registrations.
Reporting: Generates aggregated price reports using MySQL queries.


RESTful APIs:
Endpoints for CRUD operations on organizations, users, goods, entities, workplans, and price data.
Example endpoints:
GET /api/organizations: List all organizations (Dakoii Portal).
POST /api/goods: Create a new goods item (Admin Portal).
GET /api/tasks/assigned: Retrieve tasks for a field officer (Field Portal).
POST /api/prices: Submit price data (Field Portal).
PUT /api/tasks/approve: Approve submitted tasks (Admin Portal).






Key Features:

Role-Based Access Control (RBAC): Ensures users (super admin, admin, supervisor, field officer, guest) access only authorized features.
Task Workflow: Supervisors create workplans, assign tasks to field officers, and approve submitted data.
Data Validation: Ensures accurate price data entry (e.g., numeric validation, mandatory fields).
Offline Support (Field Portal): Uses local storage (HTML5 Web Storage) for temporary data caching in low-connectivity areas, with sync on reconnection.



2.3. Data Layer
The data layer uses MySQL to store and manage all system data. The database schema is designed to support the relational structure described in the provided documents.

Database Schema:
-- Organizations Table
CREATE TABLE organizations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users Table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organization_id INT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('super_admin', 'admin', 'supervisor', 'user', 'guest') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES organizations(id)
);

-- Geographical Locations Table
CREATE TABLE geo_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    province VARCHAR(100),
    country VARCHAR(100),
    district VARCHAR(100)
);

-- Goods Groups Table
CREATE TABLE goods_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL, -- e.g., Rice, Sugar, Flour
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Goods Brands Table
CREATE TABLE goods_brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    goods_group_id INT,
    name VARCHAR(100) NOT NULL, -- e.g., Roots Rice, Trukai Rice
    type ENUM('primary', 'substitute') NOT NULL,
    FOREIGN KEY (goods_group_id) REFERENCES goods_groups(id)
);

-- Goods Items Table
CREATE TABLE goods_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    goods_brand_id INT,
    name VARCHAR(100) NOT NULL, -- e.g., Roots Rice 10kg
    unit VARCHAR(50), -- e.g., 10kg, 5kg, 1kg
    FOREIGN KEY (goods_brand_id) REFERENCES goods_brands(id)
);

-- Business Houses (Entities) Table
CREATE TABLE entities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL, -- e.g., Papindo
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Business Locations Table
CREATE TABLE business_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_id INT,
    geo_location_id INT,
    name VARCHAR(255) NOT NULL, -- e.g., Wewak Town
    FOREIGN KEY (entity_id) REFERENCES entities(id),
    FOREIGN KEY (geo_location_id) REFERENCES geo_locations(id)
);

-- Workplans Table
CREATE TABLE workplans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_by INT, -- Supervisor ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Activities Table
CREATE TABLE activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    workplan_id INT,
    type ENUM('price_collection') NOT NULL,
    description TEXT,
    FOREIGN KEY (workplan_id) REFERENCES workplans(id)
);

-- Tasks Table
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    activity_id INT,
    assigned_to INT, -- Field Officer ID
    entity_id INT,
    business_location_id INT,
    goods_item_id INT,
    status ENUM('pending', 'submitted', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    FOREIGN KEY (entity_id) REFERENCES entities(id),
    FOREIGN KEY (business_location_id) REFERENCES business_locations(id),
    FOREIGN KEY (goods_item_id) REFERENCES goods_items(id)
);

-- Price Data Table
CREATE TABLE price_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT,
    goods_item_id INT,
    price DECIMAL(10, 2) NOT NULL,
    collection_date DATE NOT NULL,
    quarter ENUM('1st', '2nd', '3rd', '4th') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (goods_item_id) REFERENCES goods_items(id)
);


Key Considerations:

Indexes: Add indexes on frequently queried columns (e.g., goods_item_id, collection_date) for performance.
Data Integrity: Foreign keys ensure relational consistency.
Scalability: Partitioning on price_data by collection_date or quarter for large datasets.
Backup: Regular backups and transaction logs for data recovery.



2.4. Integration Layer
The system integrates with external components to enhance functionality:

APIs: RESTful APIs for inter-portal communication and potential third-party integrations.
File Import/Export: Support for importing/exporting price data in CSV/Excel format using PHP libraries like PhpSpreadsheet.
Authentication: Integration with CodeIgniter’s session-based authentication; potential for OAuth2 in future iterations.
Offline Sync: Field Portal uses service workers and local storage for offline data collection, syncing with the server when online.


3. System Components and Interactions
3.1. Component Diagram
[Dakoii Portal]  [Admin Portal]  [Field Portal]
       |                |               |
       |                |               |
       +----------------+----------------+----> [CodeIgniter 4 Application]
                            |
                            |
                        [MySQL Database]

3.2. Workflow

Super Admin (Dakoii Portal):
Creates organizations and assigns admins.
Defines geographical locations (e.g., province, district).


Admin/Supervisor (Admin Portal):
Registers goods (groups, brands, items) and business houses.
Creates workplans, activities, and tasks, assigning them to field officers.
Approves/rejects submitted tasks and generates price reports.


Field Officer (Field Portal):
Views assigned tasks.
Submits price data for goods at specified business locations.
Syncs data in low-bandwidth environments.


Data Flow:
Price data is stored in the price_data table, linked to tasks and goods.
Reports are generated by aggregating data by quarter, goods, or business location.




4. Non-Functional Requirements

Performance: Optimize database queries with indexes and caching (e.g., CodeIgniter’s query caching).
Scalability: Use horizontal scaling for MySQL (read replicas) and load balancing for web servers if needed.
Security:
HTTPS for secure data transmission.
Input validation and sanitization to prevent SQL injection and XSS.
RBAC to restrict access based on user roles.


Usability: Minimalist Field Portal design for low-bandwidth areas; responsive design for all portals.
Reliability: Implement transaction management for price data submissions to ensure data consistency.


5. Deployment Architecture

Server: Single server running Apache/Nginx with PHP 8.1+ for CodeIgniter 4.
Database: MySQL 8.0+ hosted on the same or separate server.
Deployment:
Use Docker for containerized deployment.
CI/CD pipeline with GitHub Actions for automated testing and deployment.


Backup: Daily database backups with automated scripts.
Monitoring: Use tools like New Relic or Prometheus for performance monitoring.


6. Future Enhancements

Mobile App: Develop a native mobile app for the Field Portal using Flutter or React Native.
Analytics: Integrate advanced analytics with visualizations using Chart.js or D3.js.
API Expansion: Expose public APIs for third-party integrations (e.g., xAI API at https://x.ai/api).
Offline Enhancements: Improve offline capabilities with Progressive Web App (PWA) features.


This architecture leverages CodeIgniter 4’s MVC framework and MySQL’s relational capabilities to deliver a robust, scalable, and user-friendly price data collection system tailored to the requirements of the Dakoii, Admin, and Field Portals.