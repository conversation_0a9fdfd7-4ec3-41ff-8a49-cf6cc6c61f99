<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit Goods Brand
                            </h2>
                            <p class="text-light mb-0">Update goods brand information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/goods-brands') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-form me-2"></i>Goods Brand Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('admin/goods-brands/' . $brand['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="goods_group_id" class="form-label text-light">
                                        Goods Group <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-control-dark" id="goods_group_id" name="goods_group_id" required>
                                        <option value="">Select Goods Group</option>
                                        <?php foreach ($groups as $id => $name): ?>
                                            <option value="<?= $id ?>" 
                                                    <?= old('goods_group_id', $brand['goods_group_id']) == $id ? 'selected' : '' ?>>
                                                <?= esc($name) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text text-muted">
                                        Select the goods group this brand belongs to
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="brand_name" class="form-label text-light">
                                        Brand Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control form-control-dark" 
                                           id="brand_name" 
                                           name="brand_name" 
                                           value="<?= old('brand_name', $brand['brand_name']) ?>" 
                                           placeholder="e.g., Roots Rice, Lucky Me"
                                           required>
                                    <div class="form-text text-muted">
                                        Enter the brand name (max 150 characters)
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="type" class="form-label text-light">
                                        Brand Type <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-control-dark" id="type" name="type" required>
                                        <option value="">Select Type</option>
                                        <option value="primary" <?= old('type', $brand['type']) === 'primary' ? 'selected' : '' ?>>
                                            Primary
                                        </option>
                                        <option value="substitute" <?= old('type', $brand['type']) === 'substitute' ? 'selected' : '' ?>>
                                            Substitute
                                        </option>
                                    </select>
                                    <div class="form-text text-muted">
                                        Primary or substitute brand
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label text-light">
                                        Status <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-control-dark" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" <?= old('status', $brand['status']) === 'active' ? 'selected' : '' ?>>
                                            Active
                                        </option>
                                        <option value="inactive" <?= old('status', $brand['status']) === 'inactive' ? 'selected' : '' ?>>
                                            Inactive
                                        </option>
                                    </select>
                                    <div class="form-text text-muted">
                                        Update the status for this brand
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Information Box -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="bi bi-info-circle me-2"></i>Brand Type Information:</h6>
                                    <ul class="mb-0">
                                        <li><strong>Primary:</strong> Main brand for the goods group</li>
                                        <li><strong>Substitute:</strong> Alternative brand that can replace the primary brand</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Metadata Information -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary mb-3">
                                    <div class="card-header">
                                        <h6 class="text-light mb-0">
                                            <i class="bi bi-info-circle me-2"></i>Record Information
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-light">Created:</small><br>
                                                <span class="text-light"><?= date('M d, Y H:i', strtotime($brand['created_at'])) ?></span>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-light">Last Updated:</small><br>
                                                <span class="text-light"><?= date('M d, Y H:i', strtotime($brand['updated_at'])) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <hr class="border-secondary">
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/goods-brands') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Update Goods Brand
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on brand name field
    document.getElementById('brand_name').focus();
    
    // Character counter for brand name
    const brandNameInput = document.getElementById('brand_name');
    const maxLength = 150;
    
    brandNameInput.addEventListener('input', function() {
        const remaining = maxLength - this.value.length;
        const helpText = this.parentNode.querySelector('.form-text');
        
        if (remaining < 30) {
            helpText.innerHTML = `Enter the brand name (${remaining} characters remaining)`;
            helpText.className = remaining < 10 ? 'form-text text-warning' : 'form-text text-info';
        } else {
            helpText.innerHTML = 'Enter the brand name (max 150 characters)';
            helpText.className = 'form-text text-muted';
        }
    });
    
    // Type selection helper
    const typeSelect = document.getElementById('type');
    const typeInfo = {
        'primary': 'Main brand for the goods group',
        'substitute': 'Alternative brand that can replace the primary brand'
    };
    
    typeSelect.addEventListener('change', function() {
        const helpText = this.parentNode.querySelector('.form-text');
        if (this.value && typeInfo[this.value]) {
            helpText.innerHTML = typeInfo[this.value];
            helpText.className = 'form-text text-info';
        } else {
            helpText.innerHTML = 'Primary or substitute brand';
            helpText.className = 'form-text text-muted';
        }
    });
});
</script>
<?= $this->endSection() ?>
