<?= $this->extend('templates/admin_template') ?>

 

<?= $this->section('content') ?>
<div class="container-fluid py-4 goods-items">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-box me-2"></i>Goods Items Management
                            </h2>
                            <p class="text-muted mb-0">
                                Manage goods items and their specifications
                                <?php if ($selected_brand): ?>
                                    - Filtered by: <strong><?= esc($selected_brand['brand_name']) ?></strong> 
                                    (<?= esc($selected_brand['group_name']) ?>)
                                <?php elseif ($selected_group): ?>
                                    - Filtered by: <strong><?= esc($selected_group['group_name']) ?></strong>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-house me-1"></i> Back to Dashboard
                            </a>
                            <a href="<?= base_url('admin/goods-items/new' .
                                      ($brand_id ? '?brand_id=' . $brand_id : 
                                       ($group_id ? '?group_id=' . $group_id : ''))) ?>" 
                               class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Add New Item
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <label for="group_filter" class="form-label text-dark me-2 mb-0">Group:</label>
                                <select class="form-select form-control-dark" id="group_filter" style="width: auto;">
                                    <option value="">All Groups</option>
                                    <?php foreach ($groups as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= $group_id == $id ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <label for="brand_filter" class="form-label text-dark me-2 mb-0">Brand:</label>
                                <select class="form-select form-control-dark" id="brand_filter" style="width: auto;">
                                    <option value="">All Brands</option>
                                    <?php foreach ($brands as $id => $name): ?>
                                        <option value="<?= $id ?>" <?= $brand_id == $id ? 'selected' : '' ?>>
                                            <?= esc($name) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?= base_url('admin/goods-groups') ?>" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-collection me-1"></i>Groups
                            </a>
                            <a href="<?= base_url('admin/goods-brands') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-tags me-1"></i>Brands
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Goods Items List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($items)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-box text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Goods Items Found</h4>
                            <p class="text-muted">
                                <?php if ($selected_brand): ?>
                                    No items found for "<?= esc($selected_brand['brand_name']) ?>".
                                <?php elseif ($selected_group): ?>
                                    No items found for "<?= esc($selected_group['group_name']) ?>".
                                <?php else: ?>
                                    Start by adding your first goods item.
                                <?php endif; ?>
                            </p>
                            <a href="<?= base_url('admin/goods-items/new' .
                                      ($brand_id ? '?brand_id=' . $brand_id : 
                                       ($group_id ? '?group_id=' . $group_id : ''))) ?>" 
                               class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Add Goods Item
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Item Name</th>
                                        <th>Group</th>
                                        <th>Brand</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="table-light">
                                    <?php foreach ($items as $index => $item): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <strong class="text-primary-custom"><?= esc($item['item']) ?></strong>
                                                <?php if (!empty($item['remarks'])): ?>
                                                    <br><small class="text-muted">
                                                        <?= esc(substr($item['remarks'], 0, 50)) ?>
                                                        <?= strlen($item['remarks']) > 50 ? '...' : '' ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= esc($item['group_name'] ?? 'Unknown') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= esc($item['brand_name'] ?? 'Unknown') ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($item['status'] === 'active'): ?>
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>Active
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="bi bi-pause-circle me-1"></i>Inactive
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M d, Y', strtotime($item['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('admin/goods-items/' . $item['id']) ?>"
                                                       class="btn btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/goods-items/' . $item['id'] . '/edit') ?>"
                                                       class="btn btn-outline-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <form method="post" action="<?= base_url('admin/goods-items/' . $item['id'] . '/delete') ?>"
                                                          class="d-inline" onsubmit="return confirm('Are you sure you want to delete this goods item?')">
                                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const groupFilter = document.getElementById('group_filter');
    const brandFilter = document.getElementById('brand_filter');
    
    // Group filter functionality
    groupFilter.addEventListener('change', function() {
        const selectedGroupId = this.value;
        const currentUrl = new URL(window.location.href);
        
        if (selectedGroupId) {
            currentUrl.searchParams.set('group_id', selectedGroupId);
            currentUrl.searchParams.delete('brand_id'); // Clear brand filter when group changes
        } else {
            currentUrl.searchParams.delete('group_id');
            currentUrl.searchParams.delete('brand_id');
        }
        
        window.location.href = currentUrl.toString();
    });
    
    // Brand filter functionality
    brandFilter.addEventListener('change', function() {
        const selectedBrandId = this.value;
        const currentUrl = new URL(window.location.href);
        
        if (selectedBrandId) {
            currentUrl.searchParams.set('brand_id', selectedBrandId);
        } else {
            currentUrl.searchParams.delete('brand_id');
        }
        
        window.location.href = currentUrl.toString();
    });
});
</script>
<?= $this->endSection() ?>
