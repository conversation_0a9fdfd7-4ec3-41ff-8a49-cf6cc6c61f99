<?php
// Test script to generate correct password hash for "dakoii"

echo "Password hash for 'dakoii':\n";
$hash = password_hash('dakoii', PASSWORD_DEFAULT);
echo $hash . "\n\n";

echo "Testing verification:\n";
$test = password_verify('dakoii', $hash);
echo "Verification result: " . ($test ? "SUCCESS" : "FAILED") . "\n\n";

echo "SQL to update your user's password:\n";
echo "UPDATE dakoii_users SET password = '$hash' WHERE username = 'fkenny';\n\n";

// Test with your current hash (replace with the full hash from your database)
$your_current_hash = '$2y$10$A.8jXDJcv/wbzVi3l8bt/OPY6B0FpExgbUg.HOk6Khq...'; // Replace ... with actual hash
echo "Testing your current hash with 'dakoii':\n";
$current_test = password_verify('dakoii', $your_current_hash);
echo "Current hash verification: " . ($current_test ? "SUCCESS" : "FAILED") . "\n";
?>
