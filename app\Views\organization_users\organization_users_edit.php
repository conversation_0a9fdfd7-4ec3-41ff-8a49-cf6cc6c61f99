<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit Organization User
                            </h2>
                            <p class="text-light mb-0">Update information for <?= esc($user['name']) ?></p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/organization-users') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= $error ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-person-fill me-2"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= base_url('dakoii/organization-users/' . $user['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <!-- Organization Selection -->
                            <div class="col-md-6 mb-3">
                                <label for="org_id" class="form-label">Organization <span class="text-danger">*</span></label>
                                <select class="form-select" id="org_id" name="org_id" required>
                                    <option value="">Select Organization</option>
                                    <?php foreach ($organizations as $org): ?>
                                        <option value="<?= $org['id'] ?>" 
                                                <?= (old('org_id') ?? $user['org_id']) == $org['id'] ? 'selected' : '' ?>>
                                            <?= esc($org['org_name']) ?> (<?= esc($org['org_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name') ?? esc($user['name']) ?>" required maxlength="255">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= old('email') ?? esc($user['email']) ?>" required maxlength="500">
                            </div>

                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="phone" name="phone" 
                                       value="<?= old('phone') ?? esc($user['phone']) ?>" required maxlength="200">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="password" name="password" minlength="6">
                                <div class="form-text">Leave blank to keep current password</div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                <div class="form-text">Required only if changing password</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Role -->
                            <div class="col-md-4 mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="user" <?= (old('role') ?? $user['role']) == 'user' ? 'selected' : '' ?>>User</option>
                                    <option value="guest" <?= (old('role') ?? $user['role']) == 'guest' ? 'selected' : '' ?>>Guest</option>
                                </select>
                            </div>

                            <!-- Status -->
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <?= (old('status') ?? $user['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= (old('status') ?? $user['status']) == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="suspended" <?= (old('status') ?? $user['status']) == 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                </select>
                            </div>

                            <!-- Position -->
                            <div class="col-md-4 mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="<?= old('position') ?? esc($user['position']) ?>" maxlength="255">
                            </div>
                        </div>

                        <div class="row">
                            <!-- ID Photo -->
                            <div class="col-md-12 mb-3">
                                <label for="id_photo" class="form-label">ID Photo Path <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="id_photo" name="id_photo" 
                                       value="<?= old('id_photo') ?? esc($user['id_photo']) ?>" required maxlength="500" 
                                       placeholder="e.g., /uploads/photos/user_photo.jpg">
                                <div class="form-text">Enter the path to the user's ID photo</div>
                            </div>
                        </div>

                        <!-- Permissions -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label">Permissions</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_admin" name="is_admin" value="1" 
                                                   <?= (old('is_admin') ?? $user['is_admin']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_admin">
                                                <i class="bi bi-shield-check me-1"></i>Admin Privileges
                                            </label>
                                            <div class="form-text">Grant administrative privileges to this user</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_supervisor" name="is_supervisor" value="1" 
                                                   <?= (old('is_supervisor') ?? $user['is_supervisor']) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_supervisor">
                                                <i class="bi bi-person-badge me-1"></i>Supervisor Privileges
                                            </label>
                                            <div class="form-text">Grant supervisory privileges to this user</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('dakoii/organization-users/' . $user['id']) ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-check-circle me-2"></i>Update User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
