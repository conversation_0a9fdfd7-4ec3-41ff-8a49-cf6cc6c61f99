<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-eye me-2"></i>View Goods Group
                            </h2>
                            <p class="text-muted mb-0">Detailed information about this goods group</p>
                        </div>
                        <div>
                            <?= view('partials/back_button', [
                                'href' => base_url('admin/goods-groups'),
                                'label' => 'Back to Groups List',
                                'class' => 'btn btn-secondary me-2'
                            ]) ?>
                            <a href="<?= base_url('admin/goods-groups/' . $group['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Information -->
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-info-circle me-2"></i>Group Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Group Name</label>
                                <div class="text-dark h5"><?= esc($group['group_name']) ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <?php if ($group['status'] === 'active'): ?>
                                        <span class="badge bg-success fs-6">
                                            <i class="bi bi-check-circle me-1"></i>Active
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary fs-6">
                                            <i class="bi bi-pause-circle me-1"></i>Inactive
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Description</label>
                                <div class="text-dark">
                                    <?php if (!empty($group['description'])): ?>
                                        <?= nl2br(esc($group['description'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted fst-italic">No description provided</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status Information -->
                    <?php if (!empty($group['status_remarks'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Status Remarks</label>
                                    <div class="text-dark"><?= nl2br(esc($group['status_remarks'])) ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Related Data Summary -->
            <div class="card card-dark mt-4">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>Related Data
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center p-3 border border-secondary rounded">
                                <i class="bi bi-tags text-primary" style="font-size: 2rem;"></i>
                                <h4 class="text-primary mt-2"><?= $brands_count ?></h4>
                                <p class="text-muted mb-0">Brands</p>
                                <a href="<?= base_url('admin/goods-brands?group_id=' . $group['id']) ?>"
                                   class="btn btn-outline-primary mt-2">
                                    <i class="bi bi-eye me-1"></i>View Brands
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center p-3 border border-secondary rounded">
                                <i class="bi bi-box text-info" style="font-size: 2rem;"></i>
                                <h4 class="text-info mt-2"><?= $items_count ?></h4>
                                <p class="text-muted mb-0">Items</p>
                                <a href="<?= base_url('admin/goods-items?group_id=' . $group['id']) ?>"
                                   class="btn btn-outline-info mt-2">
                                    <i class="bi bi-eye me-1"></i>View Items
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metadata Sidebar -->
        <div class="col-lg-4">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-clock-history me-2"></i>Record Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div class="text-dark">
                            <?= date('M d, Y', strtotime($group['created_at'])) ?><br>
                            <small class="text-muted"><?= date('H:i:s', strtotime($group['created_at'])) ?></small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Last Updated</label>
                        <div class="text-dark">
                            <?= date('M d, Y', strtotime($group['updated_at'])) ?><br>
                            <small class="text-muted"><?= date('H:i:s', strtotime($group['updated_at'])) ?></small>
                        </div>
                    </div>

                    <?php if (!empty($group['status_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Changed</label>
                            <div class="text-dark">
                                <?= date('M d, Y', strtotime($group['status_at'])) ?><br>
                                <small class="text-muted"><?= date('H:i:s', strtotime($group['status_at'])) ?></small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card card-dark mt-4">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('admin/goods-groups/' . $group['id'] . '/edit') ?>"
                           class="btn btn-warning">
                            <i class="bi bi-pencil me-2"></i>Edit Group
                        </a>

                        <a href="<?= base_url('admin/goods-brands?group_id=' . $group['id']) ?>"
                           class="btn btn-primary">
                            <i class="bi bi-tags me-2"></i>Manage Brands
                        </a>

                        <a href="<?= base_url('admin/goods-items?group_id=' . $group['id']) ?>"
                           class="btn btn-info">
                            <i class="bi bi-box me-2"></i>View Items
                        </a>

                        <?php if ($brands_count == 0 && $items_count == 0): ?>
                            <form method="post" action="<?= base_url('admin/goods-groups/' . $group['id'] . '/delete') ?>"
                                  onsubmit="return confirm('Are you sure you want to delete this goods group? This action cannot be undone.')">
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="bi bi-trash me-2"></i>Delete Group
                                </button>
                            </form>
                        <?php else: ?>
                            <button type="button" class="btn btn-danger" disabled 
                                    title="Cannot delete - has associated brands or items">
                                <i class="bi bi-trash me-2"></i>Delete Group
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
