<?php

namespace App\Controllers;

use App\Models\BusinessLocationModel;
use App\Models\BusinessEntityModel;
use App\Models\GeoCountryModel;
use App\Models\GeoProvinceModel;
use App\Models\GeoDistrictModel;
use CodeIgniter\Controller;

class BusinessLocations extends Controller
{
    protected $businessLocationModel;
    protected $businessEntityModel;
    protected $geoCountryModel;
    protected $geoProvinceModel;
    protected $geoDistrictModel;

    public function __construct()
    {
        $this->businessLocationModel = new BusinessLocationModel();
        $this->businessEntityModel = new BusinessEntityModel();
        $this->geoCountryModel = new GeoCountryModel();
        $this->geoProvinceModel = new GeoProvinceModel();
        $this->geoDistrictModel = new GeoDistrictModel();
    }
    
    /**
     * Check admin authentication
     */
    private function checkAuth()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin/login')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of business locations
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $entityFilter = $this->request->getGet('entity');
        
        if ($entityFilter) {
            $locations = $this->businessLocationModel->getByBusinessEntity($entityFilter);
            $filterEntity = $this->businessEntityModel->find($entityFilter);
        } else {
            $locations = $this->businessLocationModel->getWithBusinessEntity();
            $filterEntity = null;
        }
        
        $entities = $this->businessEntityModel->getActive();
        
        $data = [
            'title' => 'Business Locations',
            'locations' => $locations,
            'entities' => $entities,
            'filterEntity' => $filterEntity,
            'entityFilter' => $entityFilter
        ];
        
        return view('business_locations/business_locations_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $entities = $this->businessEntityModel->getActive();
        $selectedEntity = $this->request->getGet('entity');
        $countries = $this->geoCountryModel->getCountriesForDropdown();

        $data = [
            'title' => 'Create New Business Location',
            'entities' => $entities,
            'selectedEntity' => $selectedEntity,
            'countries' => $countries
        ];
        
        return view('business_locations/business_locations_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'business_entity_id' => 'required|integer',
            'business_name' => 'required|max_length[150]',
            'country_id' => 'permit_empty|integer',
            'province_id' => 'permit_empty|integer',
            'district_id' => 'permit_empty|integer',
            'gps_coordinates' => 'permit_empty|max_length[200]',
            'remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Check if business entity exists and is active
        $entity = $this->businessEntityModel->find($this->request->getPost('business_entity_id'));
        if (!$entity || $entity['status'] !== 'active') {
            return redirect()->back()->withInput()->with('error', 'Selected business entity is not available.');
        }
        
        $locationData = [
            'business_entity_id' => $this->request->getPost('business_entity_id'),
            'business_name' => $this->request->getPost('business_name'),
            'country_id' => $this->request->getPost('country_id') ?: null,
            'province_id' => $this->request->getPost('province_id') ?: null,
            'district_id' => $this->request->getPost('district_id') ?: null,
            'gps_coordinates' => $this->request->getPost('gps_coordinates'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active', // Automatically set to active
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('admin_user_id')
        ];
        
        try {
            if ($this->businessLocationModel->insert($locationData)) {
                return redirect()->to('admin/business-locations')->with('success', 'Business location created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create business location.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Show single business location
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $location = $this->businessLocationModel->getWithBusinessEntity($id);
        
        if (!$location) {
            return redirect()->to('admin/business-locations')->with('error', 'Business location not found.');
        }
        
        $data = [
            'title' => 'Business Location Details',
            'location' => $location
        ];
        
        return view('business_locations/business_locations_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $location = $this->businessLocationModel->getWithBusinessEntity($id);

        if (!$location) {
            return redirect()->to('admin/business-locations')->with('error', 'Business location not found.');
        }
        
        $entities = $this->businessEntityModel->getActive();
        $countries = $this->geoCountryModel->getCountriesForDropdown();

        $data = [
            'title' => 'Edit Business Location',
            'location' => $location,
            'entities' => $entities,
            'countries' => $countries
        ];
        
        return view('business_locations/business_locations_edit', $data);
    }
    
    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $location = $this->businessLocationModel->find($id);
        if (!$location) {
            return redirect()->to('admin/business-locations')->with('error', 'Business location not found.');
        }
        
        $rules = [
            'business_entity_id' => 'required|integer',
            'business_name' => 'required|max_length[150]',
            'country_id' => 'permit_empty|integer',
            'province_id' => 'permit_empty|integer',
            'district_id' => 'permit_empty|integer',
            'gps_coordinates' => 'permit_empty|max_length[200]',
            'remarks' => 'permit_empty',
            'status' => 'required|in_list[active,inactive]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Check if business entity exists and is active
        $entity = $this->businessEntityModel->find($this->request->getPost('business_entity_id'));
        if (!$entity || $entity['status'] !== 'active') {
            return redirect()->back()->withInput()->with('error', 'Selected business entity is not available.');
        }
        
        $locationData = [
            'business_entity_id' => $this->request->getPost('business_entity_id'),
            'business_name' => $this->request->getPost('business_name'),
            'country_id' => $this->request->getPost('country_id') ?: null,
            'province_id' => $this->request->getPost('province_id') ?: null,
            'district_id' => $this->request->getPost('district_id') ?: null,
            'gps_coordinates' => $this->request->getPost('gps_coordinates'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => $this->request->getPost('status'),
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => session()->get('admin_user_id')
        ];
        
        // Skip model validation since we already validated in controller
        $this->businessLocationModel->skipValidation(true);

        try {
            if ($this->businessLocationModel->update($id, $locationData)) {
                return redirect()->to('admin/business-locations')->with('success', 'Business location updated successfully.');
            } else {
                // Get model errors for debugging
                $errors = $this->businessLocationModel->errors();
                $errorMessage = !empty($errors) ? implode(', ', $errors) : 'Failed to update business location.';
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', $e->getMessage());
        }
    }
    
    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $location = $this->businessLocationModel->find($id);
        if (!$location) {
            return redirect()->to('admin/business-locations')->with('error', 'Business location not found.');
        }
        
        if ($this->businessLocationModel->softDelete($id)) {
            return redirect()->to('admin/business-locations')->with('success', 'Business location deleted successfully.');
        } else {
            return redirect()->to('admin/business-locations')->with('error', 'Failed to delete business location.');
        }
    }

    /**
     * Get provinces by country (AJAX)
     */
    public function getProvincesByCountry()
    {
        // Allow AJAX requests
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request']);
        }

        $countryId = $this->request->getPost('country_id');

        if (!$countryId) {
            return $this->response->setJSON(['provinces' => []]);
        }

        try {
            $provinces = $this->geoProvinceModel->getProvincesForDropdown($countryId);
            return $this->response->setJSON(['provinces' => $provinces]);
        } catch (\Exception $e) {
            log_message('error', 'Error loading provinces: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Error loading provinces']);
        }
    }

    /**
     * Get districts by province (AJAX)
     */
    public function getDistrictsByProvince()
    {
        // Allow AJAX requests
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request']);
        }

        $provinceId = $this->request->getPost('province_id');

        if (!$provinceId) {
            return $this->response->setJSON(['districts' => []]);
        }

        try {
            $districts = $this->geoDistrictModel->getDistrictsByProvinceForDropdown($provinceId);
            return $this->response->setJSON(['districts' => $districts]);
        } catch (\Exception $e) {
            log_message('error', 'Error loading districts: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Error loading districts']);
        }
    }
}
