<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-pencil me-2"></i>Edit Goods Item
                            </h2>
                            <p class="text-light mb-0">Update goods item information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/goods-items') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-form me-2"></i>Goods Item Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>Please fix the following errors:</h6>
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" action="<?= base_url('admin/goods-items/' . $item['id'] . '/update') ?>">
                        <?= csrf_field() ?>
                        
                        <!-- Group and Brand Selection -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="goods_group_id" class="form-label text-light">
                                        Goods Group <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-control-dark" id="goods_group_id" name="goods_group_id" required>
                                        <option value="">Select Goods Group</option>
                                        <?php foreach ($groups as $id => $name): ?>
                                            <option value="<?= $id ?>" 
                                                    <?= old('goods_group_id', $item['goods_group_id']) == $id ? 'selected' : '' ?>>
                                                <?= esc($name) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text text-muted">
                                        Select the goods group this item belongs to
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="goods_brand_id" class="form-label text-light">
                                        Goods Brand <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-control-dark" id="goods_brand_id" name="goods_brand_id" required>
                                        <option value="">Select Goods Brand</option>
                                        <?php foreach ($brands as $id => $name): ?>
                                            <option value="<?= $id ?>" 
                                                    <?= old('goods_brand_id', $item['goods_brand_id']) == $id ? 'selected' : '' ?>>
                                                <?= esc($name) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text text-muted">
                                        Select the brand for this item
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Item Details -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="item" class="form-label text-dark">
                                        Item Name <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-dark"
                                           id="item"
                                           name="item"
                                           value="<?= old('item', $item['item']) ?>"
                                           placeholder="e.g., Premium White Rice 25kg"
                                           required>
                                    <div class="form-text text-muted">
                                        Enter the specific item name (max 200 characters)
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Remarks -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="remarks" class="form-label text-dark">
                                        Remarks
                                    </label>
                                    <textarea class="form-control form-control-dark"
                                              id="remarks"
                                              name="remarks"
                                              rows="3"
                                              placeholder="Optional remarks about this goods item"><?= old('remarks', $item['remarks']) ?></textarea>
                                    <div class="form-text text-muted">
                                        Provide additional details about this item (optional)
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Status -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label text-dark">
                                        Status <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select form-control-dark" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="active" <?= old('status', $item['status']) === 'active' ? 'selected' : '' ?>>
                                            Active
                                        </option>
                                        <option value="inactive" <?= old('status', $item['status']) === 'inactive' ? 'selected' : '' ?>>
                                            Inactive
                                        </option>
                                    </select>
                                    <div class="form-text text-muted">
                                        Update the status for this item
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Metadata Information -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card bg-dark border-secondary mb-3">
                                    <div class="card-header">
                                        <h6 class="text-light mb-0">
                                            <i class="bi bi-info-circle me-2"></i>Record Information
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-light">Created:</small><br>
                                                <span class="text-light"><?= date('M d, Y H:i', strtotime($item['created_at'])) ?></span>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-light">Last Updated:</small><br>
                                                <span class="text-light"><?= date('M d, Y H:i', strtotime($item['updated_at'])) ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <hr class="border-secondary">
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('admin/goods-items') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Update Goods Item
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const groupSelect = document.getElementById('goods_group_id');
    const brandSelect = document.getElementById('goods_brand_id');
    
    // Auto-focus on item name field
    document.getElementById('item').focus();
    
    // Handle group change to update brands
    groupSelect.addEventListener('change', function() {
        const groupId = this.value;
        const currentBrandId = brandSelect.value;
        
        // Clear brand options
        brandSelect.innerHTML = '<option value="">Select Goods Brand</option>';
        
        if (groupId) {
            // Fetch brands for selected group
            fetch('<?= base_url('admin/goods-items/get-brands-by-group') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: 'group_id=' + groupId
            })
            .then(response => response.json())
            .then(data => {
                if (data.brands) {
                    data.brands.forEach(brand => {
                        const option = document.createElement('option');
                        option.value = brand.id;
                        option.textContent = brand.brand_name;
                        if (brand.id == currentBrandId) {
                            option.selected = true;
                        }
                        brandSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching brands:', error);
            });
        }
    });
    
    // Character counter for item name
    const itemNameInput = document.getElementById('item');
    const maxLength = 200;
    
    itemNameInput.addEventListener('input', function() {
        const remaining = maxLength - this.value.length;
        const helpText = this.parentNode.querySelector('.form-text');
        
        if (remaining < 40) {
            helpText.innerHTML = `Enter the specific item name (${remaining} characters remaining)`;
            helpText.className = remaining < 20 ? 'form-text text-warning' : 'form-text text-info';
        } else {
            helpText.innerHTML = 'Enter the specific item name (max 200 characters)';
            helpText.className = 'form-text text-muted';
        }
    });
});
</script>
<?= $this->endSection() ?>
