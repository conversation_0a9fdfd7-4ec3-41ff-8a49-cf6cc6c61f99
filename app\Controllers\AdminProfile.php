<?php

namespace App\Controllers;

use App\Models\UserModel;

class AdminProfile extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    private function checkAuth()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access the admin portal.');
        }
        return null;
    }

    // GET: admin/profile
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $userId = session()->get('admin_user_id');
        $user = $this->userModel->find($userId);
        if (!$user) {
            return redirect()->to('admin/dashboard')->with('error', 'User not found.');
        }

        $data = [
            'title' => 'My Profile',
            'user' => $user,
        ];

        return view('admin_user_profile/admin_user_profile_index', $data);
    }

    // POST: admin/profile/update
    public function update()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        if (!$this->request->is('post')) {
            return redirect()->to('admin/profile');
        }

        $userId = session()->get('admin_user_id');
        $user = $this->userModel->find($userId);
        if (!$user) {
            return redirect()->to('admin/dashboard')->with('error', 'User not found.');
        }

        $rules = [
            'name' => 'required|max_length[255]',
            'email' => "required|valid_email|max_length[500]|is_unique[users.email,id,{$userId}]",
            'position' => 'permit_empty|max_length[255]',
            'phone' => 'permit_empty|max_length[200]'
        ];

        // Add file validation only if a file is uploaded
        $idPhotoFile = $this->request->getFile('id_photo');
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            $rules['id_photo'] = 'uploaded[id_photo]|max_size[id_photo,2048]|ext_in[id_photo,jpg,jpeg,png,gif]';
        }

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload for ID photo
        $idPhotoPath = $user['id_photo']; // Keep existing by default
        if ($idPhotoFile && $idPhotoFile->isValid() && !$idPhotoFile->hasMoved()) {
            $uploadPath = FCPATH . 'uploads/id_photos/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Delete old photo if it exists (remove public/ prefix for file system path)
            if (!empty($user['id_photo']) && file_exists(FCPATH . str_replace('public/', '', $user['id_photo']))) {
                @unlink(FCPATH . str_replace('public/', '', $user['id_photo']));
            }

            $newName = $idPhotoFile->getRandomName();
            $idPhotoFile->move($uploadPath, $newName);
            $idPhotoPath = 'public/uploads/id_photos/' . $newName; // public/ prefix per convention
        }

        $updateData = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'position' => $this->request->getPost('position'),
            'phone' => $this->request->getPost('phone'),
            'id_photo' => $idPhotoPath,
            'updated_by' => $userId,
        ];

        // Skip model validation (we already validated fields above and model has required fields not part of profile)
        $this->userModel->skipValidation(true);
        $result = $this->userModel->update($userId, $updateData);
        $this->userModel->skipValidation(false);

        if ($result) {
            // Update session name and email shown in UI
            session()->set([
                'admin_name' => $updateData['name'],
                'admin_email' => $updateData['email'],
            ]);
            return redirect()->to('admin/profile')->with('success', 'Profile updated successfully.');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update profile.');
    }

    // POST: admin/profile/change-password
    public function changePassword()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        if (!$this->request->is('post')) {
            return redirect()->to('admin/profile');
        }

        $userId = session()->get('admin_user_id');
        $user = $this->userModel->find($userId);
        if (!$user) {
            return redirect()->to('admin/dashboard')->with('error', 'User not found.');
        }

        $currentPassword = $this->request->getPost('current_password');
        $newPassword = $this->request->getPost('new_password');
        $confirmPassword = $this->request->getPost('confirm_password');

        $errors = [];
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $errors[] = 'All password fields are required.';
        }
        if (!password_verify((string)$currentPassword, (string)$user['password'])) {
            $errors[] = 'Current password is incorrect.';
        }
        if (strlen((string)$newPassword) < 4) {
            $errors[] = 'New password must be at least 4 characters long.';
        }
        if ($newPassword !== $confirmPassword) {
            $errors[] = 'New password and confirmation do not match.';
        }

        if (!empty($errors)) {
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Update password (hashed by model beforeUpdate)
        $this->userModel->skipValidation(true);
        $updated = $this->userModel->update($userId, [
            'password' => $newPassword,
            'updated_by' => $userId,
        ]);
        $this->userModel->skipValidation(false);

        if ($updated) {
            return redirect()->to('admin/profile')->with('success', 'Password changed successfully.');
        }

        return redirect()->back()->with('error', 'Failed to change password.');
    }
}

