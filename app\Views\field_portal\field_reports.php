<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="card">
    <h1 class="card-title">📊 My Reports</h1>
    <p style="color: #666; margin-bottom: 0;">View your task performance and submission reports</p>
</div>

<!-- Summary Stats -->
<div class="row">
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number"><?= $stats['total_activities'] ?></span>
            <div class="stat-label">Total Activities</div>
        </div>
    </div>
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number"><?= $stats['completed_activities'] ?></span>
            <div class="stat-label">Completed</div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number"><?= $stats['pending_activities'] ?></span>
            <div class="stat-label">Pending</div>
        </div>
    </div>
    <div class="col col-6">
        <div class="card stat-card">
            <span class="stat-number" style="color: <?= $stats['overdue_activities'] > 0 ? '#dc3545' : '#28a745' ?>"><?= $stats['overdue_activities'] ?></span>
            <div class="stat-label">Overdue</div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="card">
    <h2 class="card-title">📈 Performance Metrics</h2>

    <div class="row">
        <div class="col col-6">
            <div style="text-align: center; padding: 15px; border: 1px solid #eee; border-radius: 8px; margin-bottom: 15px;">
                <div style="font-size: 24px; font-weight: 700; color: #007bff;"><?= $stats['completion_rate'] ?>%</div>
                <div style="font-size: 14px; color: #666;">My Completion Rate</div>
                <div style="font-size: 12px; color: #999; margin-top: 5px;">
                    Org Average: <?= $org_stats['completion_rate'] ?>%
                </div>
            </div>
        </div>
        <div class="col col-6">
            <div style="text-align: center; padding: 15px; border: 1px solid #eee; border-radius: 8px; margin-bottom: 15px;">
                <div style="font-size: 24px; font-weight: 700; color: #28a745;"><?= $stats['on_time_rate'] ?>%</div>
                <div style="font-size: 14px; color: #666;">On-Time Rate</div>
                <div style="font-size: 12px; color: #999; margin-top: 5px;">
                    Org Average: <?= $org_stats['on_time_rate'] ?>%
                </div>
            </div>
        </div>
    </div>

    <div style="font-size: 14px; color: #666; text-align: center; margin-top: 10px;">
        <?php if ($stats['completion_rate'] >= $org_stats['completion_rate']): ?>
            🎉 You're performing above organization average!
        <?php else: ?>
            📈 Keep working to reach organization average
        <?php endif; ?>
    </div>
</div>

<!-- Recent Activities -->
<div class="card">
    <h2 class="card-title">📋 Recent Activities</h2>

    <?php if (empty($recent_activities)): ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            <div style="font-size: 48px; margin-bottom: 15px;">📝</div>
            <div>No activities assigned yet</div>
            <div style="font-size: 14px; margin-top: 5px;">Check back later for new assignments</div>
        </div>
    <?php else: ?>
        <?php foreach ($recent_activities as $index => $activity): ?>
            <div style="border-bottom: 1px solid #eee; padding-bottom: 15px; margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                    <h3 style="margin: 0; font-size: 16px; color: #333;"><?= esc($activity['activity_name']) ?></h3>
                    <?php
                    $statusColors = [
                        'active' => '#ffc107',
                        'submitted' => '#17a2b8',
                        'approved' => '#28a745',
                        'redo' => '#fd7e14',
                        'cancelled' => '#6c757d'
                    ];
                    $statusColor = $statusColors[$activity['status']] ?? '#6c757d';
                    ?>
                    <span style="background: <?= $statusColor ?>; color: white; font-size: 10px; padding: 4px 8px; border-radius: 12px; margin-left: auto;">
                        <?= strtoupper($activity['status']) ?>
                    </span>
                </div>
                <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                    📅 Period: <?= date('M j', strtotime($activity['date_from'])) ?> - <?= date('M j, Y', strtotime($activity['date_to'])) ?><br>
                    🏷️ Type: <?= esc($activity['activity_type']) ?><br>
                    <?php if ($activity['status_at']): ?>
                        ✅ Last Updated: <?= date('M j, Y H:i', strtotime($activity['status_at'])) ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Recent Price Submissions -->
<div class="card">
    <h2 class="card-title">💰 Recent Price Submissions</h2>

    <?php if (empty($recent_submissions)): ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            <div style="font-size: 48px; margin-bottom: 15px;">💰</div>
            <div>No price submissions yet</div>
            <div style="font-size: 14px; margin-top: 5px;">Start collecting price data to see submissions here</div>
        </div>
    <?php else: ?>
        <?php foreach ($recent_submissions as $submission): ?>
            <div style="border-bottom: 1px solid #eee; padding-bottom: 15px; margin-bottom: 15px;">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                    <h3 style="margin: 0; font-size: 16px; color: #333;">
                        <?= esc($submission['location_name'] ?? 'Unknown Location') ?>
                    </h3>
                    <?php
                    $statusColors = [
                        'active' => '#ffc107',
                        'submitted' => '#17a2b8',
                        'approved' => '#28a745',
                        'redo' => '#fd7e14',
                        'cancelled' => '#6c757d'
                    ];
                    $statusColor = $statusColors[$submission['status']] ?? '#6c757d';
                    ?>
                    <span style="background: <?= $statusColor ?>; color: white; font-size: 10px; padding: 4px 8px; border-radius: 12px; margin-left: auto;">
                        <?= strtoupper($submission['status']) ?>
                    </span>
                </div>
                <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                    📅 Submitted: <?= date('M j, Y H:i', strtotime($submission['created_at'])) ?><br>
                    🛍️ Item: <?= esc($submission['item_name'] ?? 'Unknown Item') ?><br>
                    💰 Price: PGK <?= number_format($submission['price'], 2) ?>
                    <?php if (!empty($submission['remarks'])): ?>
                        <br>📝 Notes: <?= esc($submission['remarks']) ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <!-- View All Button -->
        <div style="text-align: center; margin-top: 20px;">
            <a href="<?= base_url('field/collect') ?>" class="btn btn-secondary" style="min-width: 150px;">
                📋 View All Submissions
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Activity Summary -->
<div class="card">
    <h2 class="card-title">📈 Activity Summary</h2>
    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Total Activities:</span>
            <strong><?= $stats['total_activities'] ?></strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Completed:</span>
            <strong style="color: #28a745;"><?= $stats['completed_activities'] ?></strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Pending:</span>
            <strong style="color: #ffc107;"><?= $stats['pending_activities'] ?></strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Overdue:</span>
            <strong style="color: <?= $stats['overdue_activities'] > 0 ? '#dc3545' : '#28a745' ?>;"><?= $stats['overdue_activities'] ?></strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Completion Rate:</span>
            <strong style="color: <?= $stats['completion_rate'] >= 80 ? '#28a745' : ($stats['completion_rate'] >= 60 ? '#ffc107' : '#dc3545') ?>;"><?= $stats['completion_rate'] ?>%</strong>
        </div>
        <div style="display: flex; justify-content: space-between;">
            <span>On-Time Rate:</span>
            <strong style="color: <?= $stats['on_time_rate'] >= 80 ? '#28a745' : ($stats['on_time_rate'] >= 60 ? '#ffc107' : '#dc3545') ?>;"><?= $stats['on_time_rate'] ?>%</strong>
        </div>
    </div>
</div>

<!-- Deadline Analysis -->
<?php if ($stats['total_activities'] > 0): ?>
<div class="card">
    <h2 class="card-title">⏰ Deadline Performance</h2>
    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; flex: 1;">
                <div style="font-size: 20px; font-weight: 700; color: #28a745;"><?= $stats['on_time_completions'] ?></div>
                <div style="font-size: 14px; color: #666;">On Time</div>
            </div>
            <div style="text-align: center; flex: 1;">
                <div style="font-size: 20px; font-weight: 700; color: #dc3545;"><?= $stats['late_completions'] ?></div>
                <div style="font-size: 14px; color: #666;">Late</div>
            </div>
            <div style="text-align: center; flex: 1;">
                <div style="font-size: 20px; font-weight: 700; color: #ffc107;"><?= $stats['overdue_activities'] ?></div>
                <div style="font-size: 14px; color: #666;">Overdue</div>
            </div>
        </div>

        <?php if ($stats['completed_activities'] > 0): ?>
        <div style="text-align: center; color: #666; font-size: 14px;">
            <?php if ($stats['on_time_rate'] >= 80): ?>
                🎯 Excellent deadline management!
            <?php elseif ($stats['on_time_rate'] >= 60): ?>
                📈 Good progress, keep improving!
            <?php else: ?>
                ⚠️ Focus on meeting deadlines
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>



<!-- Back Button -->
<div style="text-align: center; margin-top: 30px;">
    <a href="<?= base_url('field/dashboard') ?>" class="btn btn-secondary" style="min-width: 120px;">
        ← Back to Dashboard
    </a>
</div>





<?= $this->endSection() ?>
