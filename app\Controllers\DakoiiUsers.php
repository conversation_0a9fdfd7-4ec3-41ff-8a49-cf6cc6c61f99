<?php

namespace App\Controllers;

use App\Models\DakoiiUserModel;
use CodeIgniter\Controller;

class DakoiiUsers extends Controller
{
    protected $dakoiiUserModel;
    
    public function __construct()
    {
        $this->dakoiiUserModel = new DakoiiUserModel();
        helper('dakoii');
    }
    
    /**
     * Check if user is logged in
     */
    private function checkAuth()
    {
        if (!session()->get('dakoii_logged_in')) {
            return redirect()->to('dakoii')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of dakoii users
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $users = $this->dakoiiUserModel->findAll();
        
        $data = [
            'title' => 'Admin Users Management',
            'users' => $users
        ];
        
        return view('dakoii_users/dakoii_users_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $data = [
            'title' => 'Add New Admin User',
            'user' => []
        ];
        
        return view('dakoii_users/dakoii_users_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[dakoii_users.username]',
            'name' => 'required|max_length[255]',
            'password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[password]',
            'role' => 'required|in_list[super_admin,admin,moderator]',
            'is_active' => 'in_list[0,1]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'username' => $this->request->getPost('username'),
            'name' => $this->request->getPost('name'),
            'password' => $this->request->getPost('password'),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ?? 1,
            'created_by' => session()->get('dakoii_user_id')
        ];
        
        if ($this->dakoiiUserModel->save($data)) {
            return redirect()->to('dakoii/users')->with('success', 'Admin user created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create admin user.');
        }
    }
    
    /**
     * Show single user
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->dakoiiUserModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/users')->with('error', 'Admin user not found.');
        }
        
        $data = [
            'title' => 'View Admin User',
            'user' => $user
        ];
        
        return view('dakoii_users/dakoii_users_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->dakoiiUserModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/users')->with('error', 'Admin user not found.');
        }
        
        $data = [
            'title' => 'Edit Admin User',
            'user' => $user
        ];
        
        return view('dakoii_users/dakoii_users_edit', $data);
    }
    
    /**
     * Handle edit form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->dakoiiUserModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/users')->with('error', 'Admin user not found.');
        }
        
        $rules = [
            'username' => "required|min_length[3]|max_length[50]|is_unique[dakoii_users.username,id,{$id}]",
            'name' => 'required|max_length[255]',
            'role' => 'required|in_list[super_admin,admin,moderator]',
            'is_active' => 'in_list[0,1]'
        ];
        
        // Only validate password if provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[6]';
            $rules['confirm_password'] = 'matches[password]';
        }
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'username' => $this->request->getPost('username'),
            'name' => $this->request->getPost('name'),
            'role' => $this->request->getPost('role'),
            'is_active' => $this->request->getPost('is_active') ?? 1,
            'updated_by' => session()->get('dakoii_user_id')
        ];
        
        // Only update password if provided
        if ($this->request->getPost('password')) {
            $data['password'] = $this->request->getPost('password');
        }
        
        if ($this->dakoiiUserModel->update($id, $data)) {
            return redirect()->to('dakoii/users')->with('success', 'Admin user updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update admin user.');
        }
    }
    
    /**
     * Delete user
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->dakoiiUserModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/users')->with('error', 'Admin user not found.');
        }
        
        // Prevent deleting own account
        if ($id == session()->get('dakoii_user_id')) {
            return redirect()->to('dakoii/users')->with('error', 'You cannot delete your own account.');
        }
        
        if ($this->dakoiiUserModel->delete($id)) {
            return redirect()->to('dakoii/users')->with('success', 'Admin user deleted successfully.');
        } else {
            return redirect()->to('dakoii/users')->with('error', 'Failed to delete admin user.');
        }
    }
}
